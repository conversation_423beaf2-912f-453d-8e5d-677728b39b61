"""
وحدة لإنشاء وتصدير تقارير PDF
"""
import os
import base64
import tempfile
from datetime import datetime
import io
import pandas as pd
from fpdf import FPDF
import streamlit as st

from utils import format_currency

# نص عربي قياسي لاستخدامه في التقارير
ARABIC_TEXT = {
    'title': 'نظام إدارة ديون الطلاب',
    'report_title': 'تقرير ديون الطلاب',
    'date': 'تاريخ التقرير',
    'page': 'صفحة',
    'summary': 'ملخص',
    'total_students': 'إجمالي عدد الطلاب',
    'total_debt': 'إجمالي الديون',
    'total_paid': 'إجمالي المسدد',
    'total_remaining': 'إجمالي المتبقي',
    'avg_debt': 'متوسط الدين',
    'payment_ratio': 'نسبة السداد',
    'student_count': 'عدد الطلاب',
    'student_name': 'اسم الطالب',
    'debt_amount': 'قيمة الدين',
    'paid_amount': 'المبلغ المسدد',
    'remaining_amount': 'المبلغ المتبقي',
    'payment_percentage': 'نسبة السداد',
    'fully_paid': 'الطلاب المسددين بالكامل',
    'partially_paid': 'الطلاب المسددين جزئياً',
    'unpaid': 'الطلاب غير المسددين',
    'no_transactions': 'لا توجد معاملات مسجلة لهذا الطالب',
    'transactions': 'سجل المعاملات',
    'transaction_type': 'نوع المعاملة',
    'amount': 'المبلغ',
    'transaction_date': 'تاريخ المعاملة',
    'notes': 'ملاحظات',
    'debt_add': 'إضافة دين',
    'debt_payment': 'تسديد دين'
}

class PDF(FPDF):
    """فئة معدلة من FPDF لدعم الترويسة والتذييل باستخدام الأشكال والعناصر البديلة بدلاً من النص العربي المباشر"""
    
    def header(self):
        """ترويسة الصفحة"""
        # اللوجو
        # self.image('logo.png', 10, 8, 33)
        # العنوان (بدون نص عربي مباشر)
        self.set_font('Arial', 'B', 15)
        # نستخدم رمز '_' كعنوان مؤقت، وسيتم استبداله بالصورة أو النص المناسب عند العرض
        self.cell(0, 10, '_', 0, 1, 'C')
        self.ln(10)
    
    def footer(self):
        """تذييل الصفحة"""
        self.set_y(-15)
        self.set_font('Arial', 'I', 8)
        # نستخدم رقم الصفحة بدون نص عربي
        self.cell(0, 10, f'{self.page_no()}', 0, 0, 'C')


def export_all_students_pdf(df):
    """
    تصدير بيانات جميع الطلاب إلى ملف PDF
    
    Args:
        df (DataFrame): بيانات الطلاب
        
    Returns:
        BytesIO: كائن ذاكرة يحتوي على ملف PDF
    """
    try:
        # تحويل المبالغ من دينار إلى فلس
        export_df = df.copy()
        for col in ['Debt Amount', 'Paid Amount', 'Remaining Amount']:
            export_df[col] = export_df[col] * 1000
            export_df[f'Formatted {col}'] = export_df[col].apply(lambda x: format_currency(x))
        
        # إعداد الإحصائيات للتقرير
        total_debt = df['Debt Amount'].sum() * 1000  # تحويل إلى فلس
        total_paid = df['Paid Amount'].sum() * 1000  # تحويل إلى فلس
        total_remaining = total_debt - total_paid
        avg_debt = df['Debt Amount'].mean() * 1000  # تحويل إلى فلس
        payment_ratio = (total_paid / total_debt) * 100 if total_debt > 0 else 0
        students_count = len(df)
        
        # إنشاء PDF باستخدام ترميز باللغة الإنجليزية لتجنب مشاكل النص العربي
        pdf = PDF('L', 'mm', 'A4')  # Landscape
        pdf.add_page()
        
        # إضافة العنوان
        pdf.set_font('Arial', 'B', 16)
        pdf.cell(0, 10, "Student Debts Report", 0, 1, 'C')
        pdf.ln(5)
        
        # إضافة تاريخ التقرير
        pdf.set_font('Arial', 'I', 10)
        pdf.cell(0, 10, f'Report Date: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}', 0, 1, 'C')
        pdf.ln(5)
        
        # قسم الإحصائيات
        pdf.set_font('Arial', 'B', 14)
        pdf.cell(0, 10, "Debt Statistics Summary", 0, 1, 'C')
        pdf.ln(5)
        
        # عرض الإحصائيات في جدول
        pdf.set_font('Arial', '', 12)
        col_width = 60
        
        # الصف الأول - مؤشرات رئيسية
        header_row1 = ["Total Debt", "Total Paid", "Total Remaining"]
        for header in header_row1:
            pdf.cell(col_width, 10, header, 1, 0, 'C')
        pdf.ln()
        
        # بيانات الصف الأول
        pdf.cell(col_width, 10, format_currency(total_debt), 1, 0, 'C')
        pdf.cell(col_width, 10, format_currency(total_paid), 1, 0, 'C')
        pdf.cell(col_width, 10, format_currency(total_remaining), 1, 1, 'C')
        
        pdf.ln(5)
        
        # الصف الثاني - مؤشرات إضافية
        header_row2 = ["Average Debt", "Payment Ratio", "Student Count"]
        for header in header_row2:
            pdf.cell(col_width, 10, header, 1, 0, 'C')
        pdf.ln()
        
        # بيانات الصف الثاني
        pdf.cell(col_width, 10, format_currency(avg_debt), 1, 0, 'C')
        pdf.cell(col_width, 10, f'{payment_ratio:.1f}%', 1, 0, 'C')
        pdf.cell(col_width, 10, str(students_count), 1, 1, 'C')
        
        pdf.ln(10)
        
        # جدول بيانات الطلاب
        pdf.add_page()
        pdf.set_font('Arial', 'B', 14)
        pdf.cell(0, 10, "Student Debt Records", 0, 1, 'C')
        pdf.ln(5)
        
        # عناوين الأعمدة
        columns = ["Student Name", "Debt Amount", "Paid Amount", "Remaining Amount"]
        col_width = pdf.w / len(columns) - 10
        
        pdf.set_font('Arial', 'B', 12)
        for col in columns:
            pdf.cell(col_width, 10, col, 1, 0, 'C')
        pdf.ln()
        
        # إضافة البيانات
        pdf.set_font('Arial', '', 10)
        for _, row in export_df.iterrows():
            # معالجة الأسماء العربية
            name = row['Student Name']
            try:
                name.encode('latin-1')
            except UnicodeEncodeError:
                name = f"Student #{row.name + 1}"
                
            pdf.cell(col_width, 10, name, 1, 0, 'L')
            pdf.cell(col_width, 10, row['Formatted Debt Amount'], 1, 0, 'R')
            pdf.cell(col_width, 10, row['Formatted Paid Amount'], 1, 0, 'R')
            pdf.cell(col_width, 10, row['Formatted Remaining Amount'], 1, 1, 'R')
        
        # إضافة صفحة للإشعار
        pdf.add_page()
        pdf.set_font('Arial', 'B', 16)
        pdf.cell(0, 10, "Note About Arabic Text", 0, 1, 'C')
        pdf.ln(10)
        pdf.set_font('Arial', '', 12)
        pdf.multi_cell(0, 10, "This report may not display Arabic names correctly due to PDF encoding limitations. Please use the online application for full Arabic text support. All financial data is correctly displayed in Kuwaiti Fils.", 0, 'L')
            
        # إخراج الملف كـ string ثم تحويله إلى BytesIO
        pdf_bytes = io.BytesIO()
        pdf_str = pdf.output(dest='S')
        pdf_bytes.write(pdf_str.encode('latin1'))  # FPDF uses 'latin1' encoding
        pdf_bytes.seek(0)
        
        return pdf_bytes
    
    except Exception as e:
        # في حالة وجود خطأ، إنشاء ملف PDF بسيط يشرح الخطأ
        error_pdf = PDF()
        error_pdf.add_page()
        error_pdf.set_font('Arial', 'B', 16)
        error_pdf.cell(0, 10, "Error Generating PDF Report", 0, 1, 'C')
        error_pdf.ln(10)
        error_pdf.set_font('Arial', '', 12)
        error_pdf.multi_cell(0, 10, f"An error occurred while generating the PDF report: {str(e)}\n\nPlease view the data in the application instead.", 0, 'L')
        
        pdf_bytes = io.BytesIO()
        pdf_str = error_pdf.output(dest='S')
        pdf_bytes.write(pdf_str.encode('latin1'))
        pdf_bytes.seek(0)
        
        return pdf_bytes


def export_student_report_pdf(student_name, debt_data, transactions_df):
    """
    تصدير تقرير تفصيلي لطالب معين إلى ملف PDF
    
    Args:
        student_name (str): اسم الطالب
        debt_data (dict): بيانات دين الطالب
        transactions_df (DataFrame): سجل معاملات الطالب
        
    Returns:
        BytesIO: كائن ذاكرة يحتوي على ملف PDF
    """
    try:
        # تنسيق المبالغ من دينار إلى فلس
        debt_amount = debt_data.get('debt_amount', 0) * 1000
        paid_amount = debt_data.get('paid_amount', 0) * 1000
        remaining_amount = debt_amount - paid_amount
        
        # إنشاء PDF باستخدام أسلوب لا يعتمد على النص العربي
        pdf = PDF('P', 'mm', 'A4')  # Portrait
        pdf.add_page()
        
        # إضافة العنوان - استخدام نص إنجليزي لتجنب مشاكل الترميز
        pdf.set_font('Arial', 'B', 16)
        pdf.cell(0, 10, "Student Debt Report", 0, 1, 'C')
        
        # التعامل مع اسم الطالب العربي
        safe_name = student_name
        try:
            safe_name.encode('latin-1')
        except UnicodeEncodeError:
            safe_name = f"Student ID: {hash(student_name) % 1000}"  # رقم فريد للطالب
            
        pdf.set_font('Arial', 'B', 14)
        pdf.cell(0, 10, safe_name, 0, 1, 'C')
        pdf.ln(5)
        
        # إضافة تاريخ التقرير
        pdf.set_font('Arial', 'I', 10)
        pdf.cell(0, 10, f'Report Date: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}', 0, 1, 'C')
        pdf.ln(10)
        
        # معلومات الدين للطالب
        pdf.set_font('Arial', 'B', 14)
        pdf.cell(0, 10, "Debt Summary", 0, 1, 'C')
        pdf.ln(5)
        
        # جدول ملخص الدين
        pdf.set_font('Arial', 'B', 12)
        col_width = 60
        
        headers = ["Total Debt", "Amount Paid", "Remaining"]
        for header in headers:
            pdf.cell(col_width, 10, header, 1, 0, 'C')
        pdf.ln()
        
        pdf.set_font('Arial', '', 12)
        pdf.cell(col_width, 10, format_currency(debt_amount), 1, 0, 'C')
        pdf.cell(col_width, 10, format_currency(paid_amount), 1, 0, 'C')
        pdf.cell(col_width, 10, format_currency(remaining_amount), 1, 1, 'C')
        
        # حساب نسبة السداد
        if debt_amount > 0:
            payment_percentage = (paid_amount / debt_amount) * 100
            pdf.cell(0, 10, f"Payment Percentage: {payment_percentage:.1f}%", 0, 1, 'C')
        
        pdf.ln(20)
        
        # سجل المعاملات
        if not transactions_df.empty:
            pdf.set_font('Arial', 'B', 14)
            pdf.cell(0, 10, "Transaction History", 0, 1, 'C')
            pdf.ln(5)
            
            # إعداد DataFrame للعرض
            trans_df = transactions_df.copy()
            trans_df['Amount'] = trans_df['Amount'] * 1000
            trans_df['Formatted Amount'] = trans_df['Amount'].apply(lambda x: format_currency(x))
            
            # عناوين الأعمدة
            columns = ["Type", "Amount", "Date", "Notes"]
            col_width = pdf.w / len(columns) - 10
            
            pdf.set_font('Arial', 'B', 12)
            for col in columns:
                pdf.cell(col_width, 10, col, 1, 0, 'C')
            pdf.ln()
            
            # إضافة البيانات
            pdf.set_font('Arial', '', 10)
            for _, row in trans_df.iterrows():
                # تحويل التاريخ إلى نص
                if hasattr(row['Transaction Date'], 'strftime'):
                    date_str = row['Transaction Date'].strftime("%Y-%m-%d %H:%M:%S")
                else:
                    date_str = str(row['Transaction Date'])
                    
                # تجهيز البيانات للعرض
                transaction_type = "Debt" if row['Transaction Type'] == 'debt' else "Payment"
                amount = str(row['Formatted Amount'])
                
                # التعامل مع الملاحظات التي قد تحتوي على نص عربي
                notes = str(row['Notes']) if pd.notna(row['Notes']) else ''
                try:
                    notes.encode('latin-1')
                except UnicodeEncodeError:
                    notes = "Comment with Arabic text"
                
                # إضافة الصف
                pdf.cell(col_width, 10, transaction_type, 1, 0, 'C')
                pdf.cell(col_width, 10, amount, 1, 0, 'C')
                pdf.cell(col_width, 10, date_str, 1, 0, 'C')
                pdf.cell(col_width, 10, notes, 1, 1, 'C')
        else:
            pdf.set_font('Arial', 'I', 12)
            pdf.cell(0, 10, "No transactions recorded for this student.", 0, 1, 'C')
        
        # إضافة صفحة للإشعار حول دعم اللغة العربية
        pdf.add_page()
        pdf.set_font('Arial', 'B', 16)
        pdf.cell(0, 10, "Note About Arabic Text", 0, 1, 'C')
        pdf.ln(10)
        pdf.set_font('Arial', '', 12)
        pdf.multi_cell(0, 10, "This report may not display Arabic names correctly due to PDF encoding limitations. Please use the online application for full Arabic text support.", 0, 'L')
        
        # إخراج الملف كـ string ثم تحويله إلى BytesIO
        pdf_bytes = io.BytesIO()
        pdf_str = pdf.output(dest='S')
        pdf_bytes.write(pdf_str.encode('latin1'))  # FPDF uses 'latin1' encoding
        pdf_bytes.seek(0)
        
        return pdf_bytes
        
    except Exception as e:
        # في حالة وجود خطأ، إنشاء ملف PDF بسيط يشرح الخطأ
        error_pdf = PDF()
        error_pdf.add_page()
        error_pdf.set_font('Arial', 'B', 16)
        error_pdf.cell(0, 10, "Error Generating PDF Report", 0, 1, 'C')
        error_pdf.ln(10)
        error_pdf.set_font('Arial', '', 12)
        error_pdf.multi_cell(0, 10, f"An error occurred while generating the PDF report: {str(e)}\n\nPlease view the data in the application instead.", 0, 'L')
        
        pdf_bytes = io.BytesIO()
        pdf_str = error_pdf.output(dest='S')
        pdf_bytes.write(pdf_str.encode('latin1'))
        pdf_bytes.seek(0)
        
        return pdf_bytes


def export_payment_status_report_pdf(fully_paid_df, partially_paid_df, unpaid_df):
    """
    تصدير تقرير حالة السداد للطلاب إلى ملف PDF.
    يعرض قوائم للطلاب المسددين بالكامل، المسددين جزئياً، وغير المسددين
    
    Args:
        fully_paid_df (DataFrame): إطار بيانات الطلاب المسددين بالكامل
        partially_paid_df (DataFrame): إطار بيانات الطلاب المسددين جزئياً
        unpaid_df (DataFrame): إطار بيانات الطلاب غير المسددين
        
    Returns:
        BytesIO: كائن ذاكرة يحتوي على ملف PDF
    """
    try:
        # إنشاء PDF باستخدام أسلوب لا يعتمد على ترميز النص العربي
        pdf = PDF(orientation='L', format='A4')
        pdf.add_page()
        
        # عنوان التقرير - نستخدم طريقة التخطيط بدلاً من النص العربي
        pdf.set_font('Arial', 'B', 16)
        # استخدام أرقام بدلاً من النص العربي
        pdf.cell(0, 10, "Payment Status Report", 0, 1, 'C')
        pdf.set_font('Arial', '', 12)
        pdf.cell(0, 10, f'Date: {datetime.now().strftime("%Y-%m-%d %H:%M")}', 0, 1, 'C')
        pdf.ln(5)
        
        # حساب إجماليات
        total_students = len(fully_paid_df) + len(partially_paid_df) + len(unpaid_df)
        total_debt = (fully_paid_df['Debt Amount'].sum() + partially_paid_df['Debt Amount'].sum() + unpaid_df['Debt Amount'].sum()) * 1000
        total_paid = (fully_paid_df['Paid Amount'].sum() + partially_paid_df['Paid Amount'].sum() + unpaid_df['Paid Amount'].sum()) * 1000
        total_remaining = (fully_paid_df['Remaining Amount'].sum() + partially_paid_df['Remaining Amount'].sum() + unpaid_df['Remaining Amount'].sum()) * 1000
        
        # الإحصائيات
        pdf.set_font('Arial', 'B', 14)
        pdf.cell(0, 10, "Summary", 0, 1, 'L')
        
        # لوحة الإحصائيات
        pdf.set_font('Arial', '', 12)
        stats_data = [
            ["Total Students:", str(total_students)],
            ["Total Debt:", format_currency(total_debt)],
            ["Total Paid:", format_currency(total_paid)],
            ["Total Remaining:", format_currency(total_remaining)]
        ]
        
        for label, value in stats_data:
            pdf.cell(60, 10, label, 0, 0, 'L')
            pdf.cell(0, 10, value, 0, 1, 'L')
        
        pdf.ln(5)
        
        # جداول الطلاب
        
        # الطلاب المسددين بالكامل
        if not fully_paid_df.empty:
            pdf.set_font('Arial', 'B', 14)
            pdf.cell(0, 10, f"Fully Paid Students ({len(fully_paid_df)})", 0, 1, 'L')
            pdf.ln(2)
            
            # تحويل المبالغ إلى فلس
            display_df = fully_paid_df.copy()
            for col in ['Debt Amount', 'Paid Amount']:
                display_df[col] = display_df[col] * 1000
            
            # رؤوس الجدول
            column_widths = [120, 70, 70]
            pdf.set_font('Arial', 'B', 12)
            header_labels = ["Student Name", "Debt Amount", "Paid Amount"]
            
            for i, label in enumerate(header_labels):
                pdf.cell(column_widths[i], 10, label, 1, 0, 'C')
            pdf.ln()
            
            # بيانات الجدول
            pdf.set_font('Arial', '', 11)
            for _, row in display_df.iterrows():
                # التعامل مع الأسماء العربية عن طريق استخدام تنسيق خاص
                name = row['Student Name']
                # حاول استخدام اسم الطالب كما هو إذا كان يمكن ترميزه في latin-1
                try:
                    name.encode('latin-1')
                except UnicodeEncodeError:
                    # إذا فشل الترميز، استخدم رمزًا بديلًا
                    name = f"Student #{row.name + 1}"
                
                pdf.cell(column_widths[0], 10, name, 1, 0, 'L')
                pdf.cell(column_widths[1], 10, format_currency(row['Debt Amount']), 1, 0, 'L')
                pdf.cell(column_widths[2], 10, format_currency(row['Paid Amount']), 1, 1, 'L')
            
            pdf.ln(5)
        
        # الطلاب المسددين جزئياً
        if not partially_paid_df.empty:
            pdf.add_page()
            pdf.set_font('Arial', 'B', 14)
            pdf.cell(0, 10, f"Partially Paid Students ({len(partially_paid_df)})", 0, 1, 'L')
            pdf.ln(2)
            
            # تحويل المبالغ إلى فلس
            display_df = partially_paid_df.copy()
            for col in ['Debt Amount', 'Paid Amount', 'Remaining Amount']:
                display_df[col] = display_df[col] * 1000
            
            # رؤوس الجدول
            column_widths = [80, 50, 50, 50, 30]
            pdf.set_font('Arial', 'B', 12)
            header_labels = ["Student Name", "Debt Amount", "Paid Amount", "Remaining", "% Paid"]
            
            for i, label in enumerate(header_labels):
                pdf.cell(column_widths[i], 10, label, 1, 0, 'C')
            pdf.ln()
            
            # بيانات الجدول
            pdf.set_font('Arial', '', 11)
            for _, row in display_df.iterrows():
                # التعامل مع الأسماء العربية
                name = row['Student Name']
                try:
                    name.encode('latin-1')
                except UnicodeEncodeError:
                    name = f"Student #{row.name + 1}"
                
                pdf.cell(column_widths[0], 10, name, 1, 0, 'L')
                pdf.cell(column_widths[1], 10, format_currency(row['Debt Amount']), 1, 0, 'L')
                pdf.cell(column_widths[2], 10, format_currency(row['Paid Amount']), 1, 0, 'L')
                pdf.cell(column_widths[3], 10, format_currency(row['Remaining Amount']), 1, 0, 'L')
                pdf.cell(column_widths[4], 10, f"{row['Payment Percentage']}%", 1, 1, 'C')
            
            pdf.ln(5)
        
        # الطلاب غير المسددين
        if not unpaid_df.empty:
            pdf.add_page()
            pdf.set_font('Arial', 'B', 14)
            pdf.cell(0, 10, f"Unpaid Students ({len(unpaid_df)})", 0, 1, 'L')
            pdf.ln(2)
            
            # تحويل المبالغ إلى فلس
            display_df = unpaid_df.copy()
            display_df['Debt Amount'] = display_df['Debt Amount'] * 1000
            
            # رؤوس الجدول
            column_widths = [160, 100]
            pdf.set_font('Arial', 'B', 12)
            header_labels = ["Student Name", "Debt Amount"]
            
            for i, label in enumerate(header_labels):
                pdf.cell(column_widths[i], 10, label, 1, 0, 'C')
            pdf.ln()
            
            # بيانات الجدول
            pdf.set_font('Arial', '', 11)
            for _, row in display_df.iterrows():
                # التعامل مع الأسماء العربية
                name = row['Student Name']
                try:
                    name.encode('latin-1')
                except UnicodeEncodeError:
                    name = f"Student #{row.name + 1}"
                
                pdf.cell(column_widths[0], 10, name, 1, 0, 'L')
                pdf.cell(column_widths[1], 10, format_currency(row['Debt Amount']), 1, 1, 'L')
        
        # إضافة صفحة للإشعار
        pdf.add_page()
        pdf.set_font('Arial', 'B', 16)
        pdf.cell(0, 10, "Note About Arabic Text", 0, 1, 'C')
        pdf.ln(10)
        pdf.set_font('Arial', '', 12)
        pdf.multi_cell(0, 10, "This report may not display Arabic names correctly due to PDF encoding limitations. Please use the online application for full Arabic text support.", 0, 'L')
        
        # تحويل PDF إلى كائن ذاكرة
        pdf_bytes = io.BytesIO()
        # استخدام 'B' بدلاً من 'S' للحصول على البيانات الثنائية مباشرة
        # يتجنب هذا مشاكل الترميز بالكامل
        pdf_bytes.write(pdf.output(dest='B'))
        
        pdf_bytes.seek(0)
        return pdf_bytes
        
    except Exception as e:
        # في حالة وجود خطأ، إنشاء ملف PDF بسيط يشرح الخطأ
        error_pdf = PDF()
        error_pdf.add_page()
        error_pdf.set_font('Arial', 'B', 16)
        error_pdf.cell(0, 10, "Error Generating PDF Report", 0, 1, 'C')
        error_pdf.ln(10)
        error_pdf.set_font('Arial', '', 12)
        error_pdf.multi_cell(0, 10, f"An error occurred while generating the PDF report: {str(e)}\n\nPlease view the data in the application instead.", 0, 'L')
        
        pdf_bytes = io.BytesIO()
        # استخدام 'B' بدلاً من 'S' للحصول على البيانات الثنائية مباشرة
        pdf_bytes.write(error_pdf.output(dest='B'))
            
        pdf_bytes.seek(0)
        
        return pdf_bytes


def create_download_link(pdf_bytes, filename):
    """إنشاء رابط تنزيل للملف"""
    b64 = base64.b64encode(pdf_bytes.read()).decode()
    return f'<a href="data:application/octet-stream;base64,{b64}" download="{filename}">انقر هنا للتنزيل</a>'