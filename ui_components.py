"""
Módulo para componentes de UI reutilizables.
Proporciona funciones para crear tarjetas, insignias y otros elementos de UI.
"""

import streamlit as st

def card(title, content, icon=None, color=None):
    """
    Crea una tarjeta moderna con título, contenido e icono opcional
    
    Args:
        title (str): Título de la tarjeta
        content (str): Contenido HTML de la tarjeta
        icon (str, optional): Emoji o icono para la tarjeta
        color (str, optional): Color para el borde superior de la tarjeta
    """
    color_style = f"border-top: 3px solid {color};" if color else ""
    
    st.markdown(f"""
    <div style="background-color: var(--surface); border-radius: 10px; padding: 1.5rem; 
                margin-bottom: 1rem; box-shadow: 0 2px 8px rgba(0,0,0,0.1); {color_style}">
        <div style="display: flex; align-items: center; margin-bottom: 1rem;">
            {f'<div style="margin-left: 10px; font-size: 1.5rem;">{icon}</div>' if icon else ''}
            <h3 style="margin: 0; color: var(--primary);">{title}</h3>
        </div>
        <div>
            {content}
        </div>
    </div>
    """, unsafe_allow_html=True)

def metric_card(title, value, delta=None, prefix="", suffix=""):
    """
    Crea una tarjeta de métrica moderna con valor y cambio opcional
    
    Args:
        title (str): Título de la métrica
        value (str/int/float): Valor principal
        delta (float, optional): Cambio (positivo o negativo)
        prefix (str, optional): Prefijo para el valor (ej: "$")
        suffix (str, optional): Sufijo para el valor (ej: "%")
    """
    delta_html = ""
    if delta is not None:
        color = "var(--success)" if delta >= 0 else "var(--error)"
        arrow = "↑" if delta >= 0 else "↓"
        delta_html = f'<span style="color: {color}; font-size: 0.9rem;">{arrow} {abs(delta)}{suffix}</span>'
    
    st.markdown(f"""
    <div style="background-color: var(--surface); border-radius: 10px; padding: 1.2rem;
                text-align: center; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
        <div style="color: var(--text-secondary); font-size: 0.9rem;">{title}</div>
        <div style="font-size: 1.8rem; font-weight: bold; color: var(--text); margin: 0.5rem 0;">
            {prefix}{value}{suffix}
        </div>
        {delta_html}
    </div>
    """, unsafe_allow_html=True)

def status_badge(text, status):
    """
    Crea una insignia de estado con color según el estado
    
    Args:
        text (str): Texto de la insignia
        status (str): Estado ('success', 'warning', 'error', 'info')
        
    Returns:
        str: HTML para la insignia
    """
    colors = {
        "success": "var(--success)",
        "warning": "var(--warning)",
        "error": "var(--error)",
        "info": "var(--info)"
    }
    color = colors.get(status, "var(--text-secondary)")
    
    return f"""
    <span style="background-color: {color}20; color: {color}; 
               padding: 0.3rem 0.6rem; border-radius: 1rem; font-size: 0.8rem; 
               display: inline-block; font-weight: 500;">
        {text}
    </span>
    """

def payment_status_badge(percentage):
    """
    Crea una insignia de estado de pago basada en el porcentaje
    
    Args:
        percentage (float): Porcentaje de pago (0-100)
        
    Returns:
        str: HTML para la insignia
    """
    if percentage >= 100:
        return status_badge("مسدد بالكامل", "success")
    elif percentage >= 50:
        return status_badge(f"مسدد جزئياً ({percentage:.1f}%)", "info")
    elif percentage > 0:
        return status_badge(f"مسدد جزئياً ({percentage:.1f}%)", "warning")
    else:
        return status_badge("غير مسدد", "error")

def info_box(message, type="info"):
    """
    Crea un cuadro de información con estilo mejorado
    
    Args:
        message (str): Mensaje a mostrar
        type (str): Tipo de mensaje ('info', 'success', 'warning', 'error')
    """
    icons = {
        "info": "ℹ️",
        "success": "✅",
        "warning": "⚠️",
        "error": "❌"
    }
    
    colors = {
        "info": "var(--info)",
        "success": "var(--success)",
        "warning": "var(--warning)",
        "error": "var(--error)"
    }
    
    icon = icons.get(type, "ℹ️")
    color = colors.get(type, "var(--info)")
    
    st.markdown(f"""
    <div style="background-color: {color}10; border-right: 4px solid {color};
                padding: 1rem; border-radius: 4px; margin-bottom: 1rem;">
        <div style="display: flex; align-items: center;">
            <div style="margin-left: 0.5rem; font-size: 1.2rem;">{icon}</div>
            <div style="color: var(--text);">{message}</div>
        </div>
    </div>
    """, unsafe_allow_html=True)

def create_dashboard_layout():
    """
    Crea un contenedor para el dashboard con estilo mejorado
    """
    st.markdown("""
    <style>
    .dashboard-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }
    </style>
    <div class="dashboard-container">
    """, unsafe_allow_html=True)
    
    # El contenido va aquí
    
    st.markdown("</div>", unsafe_allow_html=True)
