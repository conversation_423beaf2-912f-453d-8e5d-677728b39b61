# تعليمات إنشاء ملف مضغوط للنظام للتوزيع

يمكنك اتباع الخطوات التالية لإنشاء ملف مضغوط يحتوي على التطبيق وجميع الملفات اللازمة لتثبيته بسهولة على خادمك الخاص.

## الملفات التي يجب تضمينها في الملف المضغوط:

1. **ملفات التطبيق الأساسية**:
   - `app.py` - الملف الرئيسي للتطبيق
   - `database.py` - إعدادات قاعدة البيانات
   - `debt_manager.py` - إدارة الديون
   - `utils.py` - وظائف مساعدة
   - `visualization.py` - عرض البيانات والرسوم البيانية

2. **ملفات التثبيت والتوثيق**:
   - `README.md` - توثيق مفصل للنظام
   - `INSTALL.txt` - تعليمات التثبيت السريعة
   - `setup_database.py` - سكريبت إعداد قاعدة البيانات
   - `install.sh` - سكريبت التثبيت التلقائي

3. **مجلد البيانات (اختياري)**:
   - أي ملفات بيانات أو قوالب

## خطوات إنشاء الملف المضغوط:

### على نظام Linux أو macOS:

1. افتح نافذة Terminal
2. انتقل إلى المجلد الذي يحتوي على مشروعك:
   ```bash
   cd /path/to/your/project
   ```

3. استخدم الأمر التالي لإنشاء ملف مضغوط:
   ```bash
   zip -r student_debt_system.zip app.py database.py debt_manager.py utils.py visualization.py README.md INSTALL.txt setup_database.py install.sh
   ```

### على نظام Windows:

1. حدد جميع الملفات التي تريد تضمينها
2. انقر بزر الماوس الأيمن واختر "إرسال إلى" ثم "مجلد مضغوط"
3. أعد تسمية الملف المضغوط الناتج إلى `student_debt_system.zip`

## تعليمات لاستخدام الملف المضغوط:

1. **تحميل الملف المضغوط إلى الخادم**:
   ```bash
   scp student_debt_system.zip user@your_server:/path/to/destination/
   ```

2. **تسجيل الدخول إلى الخادم وفك ضغط الملفات**:
   ```bash
   ssh user@your_server
   cd /path/to/destination/
   unzip student_debt_system.zip -d student_debt_system
   cd student_debt_system
   ```

3. **جعل سكريبت التثبيت قابل للتنفيذ وتشغيله**:
   ```bash
   chmod +x install.sh
   ./install.sh
   ```

4. **اتبع التعليمات التي تظهر أثناء التثبيت**:
   - أدخل بيانات قاعدة البيانات عندما يُطلب منك ذلك
   - سيقوم السكريبت بإعداد قاعدة البيانات وكل ما هو مطلوب

5. **تشغيل التطبيق**:
   ```bash
   streamlit run app.py --server.port 8501 --server.address 0.0.0.0
   ```

6. **الوصول إلى التطبيق**:
   - افتح المتصفح وانتقل إلى: `http://your_server_ip:8501`

## ملحوظات هامة للتوزيع:

- تأكد من أن سكريبت التثبيت لديه أذونات التنفيذ قبل التوزيع
- اجعل التعليمات واضحة وسهلة الفهم للمستخدمين النهائيين
- يفضل اختبار عملية التثبيت على بيئة نظيفة قبل التوزيع
- تأكد من عدم تضمين أي بيانات حساسة أو معلومات اعتماد في الملفات المضغوطة