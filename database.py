import os
import pandas as pd
import datetime
from sqlalchemy import create_engine, Column, Integer, String, Float, MetaData, Table, ForeignKey, DateTime
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
import streamlit as st

# إنشاء اتصال بقاعدة البيانات
DATABASE_URL = os.environ.get('DATABASE_URL')

# التحقق من توفر رابط قاعدة البيانات
if not DATABASE_URL:
    print("تحذير: لم يتم تحديد رابط قاعدة البيانات DATABASE_URL في متغيرات البيئة")
    # استخدام قاعدة بيانات محلية كحل بديل للتطوير
    DATABASE_URL = "sqlite:///student_debts.db"

engine = create_engine(DATABASE_URL)
Base = declarative_base()
SessionLocal = sessionmaker(bind=engine)

# تعريف نموذج الدين
class Debt(Base):
    """نموذج الدين في قاعدة البيانات"""
    __tablename__ = "debts"
    
    id = Column(Integer, primary_key=True, index=True)
    student_name = Column(String, unique=True, index=True)
    debt_amount = Column(Float)
    paid_amount = Column(Float, default=0.0)
    
    # العلاقة مع سجلات المعاملات
    transactions = relationship("Transaction", back_populates="student", cascade="all, delete-orphan")

# تعريف نموذج المعاملات
class Transaction(Base):
    """نموذج المعاملات (الدين والتسديد) في قاعدة البيانات"""
    __tablename__ = "transactions"
    
    id = Column(Integer, primary_key=True, index=True)
    student_id = Column(Integer, ForeignKey("debts.id"))
    transaction_type = Column(String)  # "debt" للدين، "payment" للتسديد
    amount = Column(Float)
    transaction_date = Column(DateTime, default=datetime.datetime.now)
    notes = Column(String, nullable=True)
    
    # العلاقة مع الطالب
    student = relationship("Debt", back_populates="transactions")

# إنشاء الجداول إذا لم تكن موجودة
Base.metadata.create_all(bind=engine)

class DatabaseManager:
    """
    فئة لإدارة عمليات قاعدة البيانات الخاصة بديون الطلاب
    """
    
    def __init__(self):
        self.SessionLocal = SessionLocal
    
    def add_debt(self, student_name, debt_amount, notes=None):
        """
        إضافة سجل دين جديد
        
        Args:
            student_name (str): اسم الطالب
            debt_amount (float): قيمة الدين
            notes (str, optional): ملاحظات إضافية عن الدين
            
        Returns:
            bool: True إذا نجحت العملية، False خلاف ذلك
        """
        db = None
        try:
            db = self.SessionLocal()
            
            # التحقق مما إذا كان الطالب موجودًا بالفعل
            existing_debt = db.query(Debt).filter(Debt.student_name == student_name).first()
            if existing_debt:
                db.close()
                return False
            
            # إنشاء سجل دين جديد
            new_debt = Debt(student_name=student_name, debt_amount=debt_amount)
            db.add(new_debt)
            db.flush()  # للحصول على معرف السجل الجديد
            
            # إضافة سجل معاملة للدين الأولي
            new_transaction = Transaction(
                student_id=new_debt.id,
                transaction_type="debt",
                amount=debt_amount,
                notes=notes
            )
            db.add(new_transaction)
            
            db.commit()
            db.close()
            return True
        except Exception as e:
            print(f"خطأ في إضافة الدين: {e}")
            if db:
                db.rollback()
                db.close()
            return False
    
    def update_debt(self, student_name, debt_amount, is_additional=False, notes=None):
        """
        تحديث سجل دين موجود
        
        Args:
            student_name (str): اسم الطالب
            debt_amount (float): قيمة الدين الجديدة أو المبلغ المراد إضافته
            is_additional (bool): إذا كان True، فسيتم إضافة المبلغ إلى الدين الحالي بدلاً من تغييره
            notes (str, optional): ملاحظات إضافية عن الدين
            
        Returns:
            bool: True إذا نجحت العملية، False خلاف ذلك
        """
        db = None
        try:
            # طباعة معلومات للتصحيح
            print(f"تحديث دين: الطالب = {student_name}, القيمة = {debt_amount}, إضافة = {is_additional}")
            
            db = self.SessionLocal()
            
            # البحث عن سجل الدين
            debt = db.query(Debt).filter(Debt.student_name == student_name).first()
            if not debt:
                print(f"لم يتم العثور على الطالب: {student_name}")
                db.close()
                return False
            
            # تخزين القيمة القديمة للمقارنة
            old_debt_amount = debt.debt_amount
            print(f"قيمة الدين الحالية: {old_debt_amount}")
            
            # تحديد المبلغ المراد تسجيله في سجل المعاملات
            transaction_amount = debt_amount
            if not is_additional:
                # في حالة تعيين قيمة جديدة، نحسب الفرق
                transaction_amount = debt_amount - debt.debt_amount
            
            # تحديث قيمة الدين
            if is_additional:
                # إضافة المبلغ إلى الدين الحالي
                debt.debt_amount += debt_amount
            else:
                # تعيين قيمة دين جديدة
                debt.debt_amount = debt_amount
            
            print(f"قيمة الدين الجديدة: {debt.debt_amount}")
            
            # إضافة سجل معاملة للدين (فقط إذا كان المبلغ موجبًا)
            if transaction_amount != 0:  # تم تغيير الشرط ليشمل كل المعاملات غير الصفرية
                new_transaction = Transaction(
                    student_id=debt.id,
                    transaction_type="debt",
                    amount=transaction_amount,
                    notes=notes
                )
                db.add(new_transaction)
                print(f"تمت إضافة معاملة دين جديدة بقيمة: {transaction_amount}")
            elif is_additional and debt_amount > 0:
                # حالة خاصة: إضافة مبلغ جديد للدين
                new_transaction = Transaction(
                    student_id=debt.id,
                    transaction_type="debt",
                    amount=debt_amount,
                    notes=notes
                )
                db.add(new_transaction)
                print(f"تمت إضافة معاملة دين جديدة بقيمة الإضافة: {debt_amount}")
                
            db.commit()
            print(f"تم الحفظ بنجاح، القيمة الجديدة: {debt.debt_amount}")
            db.close()
            return True
        except Exception as e:
            print(f"خطأ في تحديث الدين: {e}")
            if db:
                db.rollback()
                db.close()
            return False
            
    def add_to_debt(self, student_name, amount_to_add, notes=None):
        """
        إضافة مبلغ إلى دين طالب موجود
        
        Args:
            student_name (str): اسم الطالب
            amount_to_add (float): المبلغ المراد إضافته إلى الدين الحالي
            notes (str, optional): ملاحظات إضافية عن عملية الدين
            
        Returns:
            bool: True إذا نجحت العملية، False خلاف ذلك
        """
        return self.update_debt(student_name, amount_to_add, is_additional=True, notes=notes)
    
    def update_paid_amount(self, student_name, paid_amount, is_additional=False, notes=None):
        """
        تحديث المبلغ المسدد لسجل دين موجود
        
        Args:
            student_name (str): اسم الطالب
            paid_amount (float): المبلغ المسدد الجديد أو المبلغ المراد إضافته
            is_additional (bool): إذا كان True، فسيتم إضافة المبلغ إلى المسدد الحالي بدلاً من تغييره
            notes (str, optional): ملاحظات إضافية عن عملية التسديد
            
        Returns:
            bool: True إذا نجحت العملية، False خلاف ذلك
        """
        db = None
        try:
            # طباعة معلومات للتصحيح
            print(f"تحديث المبلغ المسدد: الطالب = {student_name}, القيمة = {paid_amount}, إضافة = {is_additional}")
            
            db = self.SessionLocal()
            
            # البحث عن سجل الدين
            debt = db.query(Debt).filter(Debt.student_name == student_name).first()
            if not debt:
                print(f"لم يتم العثور على الطالب: {student_name}")
                db.close()
                return False
            
            # تخزين القيمة القديمة للمقارنة
            old_paid_amount = debt.paid_amount
            print(f"المبلغ المسدد الحالي: {old_paid_amount}")
            
            # تحديد المبلغ المراد تسجيله في سجل المعاملات
            transaction_amount = paid_amount
            if not is_additional:
                # في حالة تعيين قيمة جديدة، نحسب الفرق
                transaction_amount = paid_amount - debt.paid_amount
            
            # تحديث قيمة المبلغ المسدد
            if is_additional:
                # إضافة المبلغ إلى المسدد الحالي
                debt.paid_amount += paid_amount
            else:
                # تعيين قيمة المسدد الجديدة
                debt.paid_amount = paid_amount
            
            print(f"المبلغ المسدد الجديد: {debt.paid_amount}")
            
            # إضافة سجل معاملة للتسديد - نسجل كل المعاملات حتى لو كانت بقيمة صفر
            # (هذا يحل مشكلة عدم تسجيل الدفعات)
            # قد تكون المعاملة بقيمة صفر أو سالبة في حالات معينة
            if transaction_amount != 0:  # لا داعي لتسجيل معاملات بقيمة صفر
                new_transaction = Transaction(
                    student_id=debt.id,
                    transaction_type="payment",
                    amount=transaction_amount,
                    notes=notes
                )
                db.add(new_transaction)
                print(f"تمت إضافة معاملة دفع جديدة بقيمة: {transaction_amount}")
            elif is_additional and paid_amount > 0:
                # في حالة الإضافة وكان المبلغ المضاف أكبر من صفر
                # نضيف معاملة جديدة بهذا المبلغ
                new_transaction = Transaction(
                    student_id=debt.id,
                    transaction_type="payment",
                    amount=paid_amount,  # استخدام المبلغ المدخل مباشرة
                    notes=notes
                )
                db.add(new_transaction)
                print(f"تمت إضافة معاملة دفع جديدة بقيمة الإضافة: {paid_amount}")
                
            db.commit()
            print(f"تم الحفظ بنجاح، المبلغ المسدد الجديد: {debt.paid_amount}")
            db.close()
            return True
        except Exception as e:
            print(f"خطأ في تحديث المبلغ المسدد: {e}")
            if db:
                db.rollback()
                db.close()
            return False
            
    def add_to_paid(self, student_name, amount_to_add, notes=None):
        """
        إضافة مبلغ إلى المسدد الحالي لطالب موجود
        
        Args:
            student_name (str): اسم الطالب
            amount_to_add (float): المبلغ المراد إضافته إلى المسدد الحالي
            notes (str, optional): ملاحظات إضافية عن عملية التسديد
            
        Returns:
            bool: True إذا نجحت العملية، False خلاف ذلك
        """
        return self.update_paid_amount(student_name, amount_to_add, is_additional=True, notes=notes)
    
    def get_paid_amount(self, student_name):
        """
        الحصول على المبلغ المسدد لطالب معين
        
        Args:
            student_name (str): اسم الطالب
            
        Returns:
            float or None: المبلغ المسدد إذا وُجد الطالب، None خلاف ذلك
        """
        db = None
        try:
            db = self.SessionLocal()
            
            # البحث عن سجل الدين
            debt = db.query(Debt).filter(Debt.student_name == student_name).first()
            db.close()
            
            if not debt:
                return None
            
            return debt.paid_amount
        except Exception as e:
            print(f"خطأ في الحصول على المبلغ المسدد: {e}")
            if db:
                db.close()
            return None
    
    def find_similar_names(self, partial_name, limit=5):
        """
        البحث عن أسماء مشابهة للاسم المدخل
        
        Args:
            partial_name (str): جزء من اسم الطالب للبحث عنه
            limit (int): الحد الأقصى لعدد النتائج المرجعة
            
        Returns:
            list: قائمة بأسماء الطلاب المشابهة
        """
        db = None
        try:
            if not partial_name or len(partial_name.strip()) < 2:
                return []
                
            db = self.SessionLocal()
            
            # البحث عن الأسماء المشابهة
            similar_names = db.query(Debt.student_name).filter(
                Debt.student_name.ilike(f"%{partial_name}%")
            ).limit(limit).all()
            
            db.close()
            
            # استخراج الأسماء من النتائج
            return [name[0] for name in similar_names]
        except Exception as e:
            print(f"خطأ في البحث عن الأسماء المشابهة: {e}")
            if db:
                db.close()
            return []
    
    def delete_debt(self, student_name):
        """
        حذف سجل دين
        
        Args:
            student_name (str): اسم الطالب
            
        Returns:
            bool: True إذا نجحت العملية، False خلاف ذلك
        """
        db = None
        try:
            db = self.SessionLocal()
            
            # البحث عن سجل الدين
            debt = db.query(Debt).filter(Debt.student_name == student_name).first()
            if not debt:
                db.close()
                return False
            
            # حذف سجل الدين
            db.delete(debt)
            db.commit()
            db.close()
            return True
        except Exception as e:
            print(f"خطأ في حذف الدين: {e}")
            if db:
                db.close()
            return False
            
    def clear_all_data(self):
        """
        إفراغ جميع البيانات من قاعدة البيانات
        
        Returns:
            bool: True إذا نجحت العملية، False خلاف ذلك
        """
        db = None
        try:
            db = self.SessionLocal()
            
            # حذف جميع سجلات المعاملات والديون
            db.query(Transaction).delete()
            db.query(Debt).delete()
            
            db.commit()
            db.close()
            return True
        except Exception as e:
            print(f"خطأ في إفراغ البيانات: {e}")
            if db:
                db.rollback()
                db.close()
            return False
    
    def get_debt(self, student_name):
        """
        الحصول على قيمة دين طالب معين
        
        Args:
            student_name (str): اسم الطالب
            
        Returns:
            float or None: قيمة الدين إذا وُجد الطالب، None خلاف ذلك
        """
        db = None
        try:
            db = self.SessionLocal()
            
            # البحث عن سجل الدين
            debt = db.query(Debt).filter(Debt.student_name == student_name).first()
            db.close()
            
            if not debt:
                return None
            
            return debt.debt_amount
        except Exception as e:
            print(f"خطأ في الحصول على الدين: {e}")
            if db:
                db.close()
            return None
    
    def get_all_students(self):
        """
        الحصول على قائمة بأسماء جميع الطلاب
        
        Returns:
            list: قائمة بأسماء الطلاب
        """
        db = None
        try:
            db = self.SessionLocal()
            
            # الحصول على جميع سجلات الديون
            debts = db.query(Debt).all()
            db.close()
            
            return [debt.student_name for debt in debts]
        except Exception as e:
            print(f"خطأ في الحصول على جميع الطلاب: {e}")
            if db:
                db.close()
            return []
    
    def get_total_debt(self):
        """
        حساب إجمالي الدين لجميع الطلاب
        
        Returns:
            float: إجمالي قيمة الدين
        """
        db = None
        try:
            db = self.SessionLocal()
            
            # الحصول على جميع سجلات الديون
            debts = db.query(Debt).all()
            db.close()
            
            return sum(debt.debt_amount for debt in debts)
        except Exception as e:
            print(f"خطأ في حساب إجمالي الدين: {e}")
            if db:
                db.close()
            return 0.0
    
    def get_dataframe(self):
        """
        الحصول على DataFrame يحتوي على جميع سجلات الديون
        
        Returns:
            DataFrame: إطار بيانات سجلات الديون
        """
        db = None
        try:
            db = self.SessionLocal()
            
            # الحصول على جميع سجلات الديون
            debts = db.query(Debt).all()
            db.close()
            
            data = {
                'Student Name': [debt.student_name for debt in debts],
                'Debt Amount': [debt.debt_amount for debt in debts],
                'Paid Amount': [debt.paid_amount for debt in debts],
                'Remaining Amount': [debt.debt_amount - debt.paid_amount for debt in debts]
            }
            
            return pd.DataFrame(data)
        except Exception as e:
            print(f"خطأ في الحصول على DataFrame: {e}")
            if db:
                db.close()
            return pd.DataFrame(columns=['Student Name', 'Debt Amount', 'Paid Amount', 'Remaining Amount'])
    
    def is_empty(self):
        """
        التحقق مما إذا كانت هناك أي سجلات ديون
        
        Returns:
            bool: True إذا لم تكن هناك سجلات، False خلاف ذلك
        """
        db = None
        try:
            db = self.SessionLocal()
            
            # التحقق من وجود أي سجلات ديون
            count = db.query(Debt).count()
            db.close()
            
            return count == 0
        except Exception as e:
            print(f"خطأ في التحقق من وجود سجلات: {e}")
            if db:
                db.close()
            return True
    
    def import_from_dataframe(self, df, replace_existing=False, update_existing=True):
        """
        استيراد سجلات الديون من DataFrame
        
        Args:
            df (DataFrame): إطار بيانات يحتوي على سجلات الديون
            replace_existing (bool): حذف جميع السجلات الحالية قبل الاستيراد
            update_existing (bool): تحديث السجلات الموجودة بدلاً من تخطيها
            
        Returns:
            bool: True إذا نجحت العملية، False خلاف ذلك
        """
        db = None
        try:
            db = self.SessionLocal()
            
            # إذا كان مطلوبًا حذف جميع السجلات الحالية
            if replace_existing:
                db.query(Debt).delete()
                print("تم حذف جميع السجلات الحالية قبل الاستيراد")
            
            # تاريخ الاستيراد
            import_date = datetime.datetime.now()
            
            # عدادات لمتابعة حالة الاستيراد
            added_count = 0
            updated_count = 0
            skipped_count = 0
            error_count = 0
            
            # إضافة السجلات الجديدة
            for _, row in df.iterrows():
                try:
                    # التعامل مع الأعمدة الإلزامية
                    student_name = row['Student Name']
                    debt_amount = float(row['Debt Amount'])
                    
                    # التعامل مع عمود المبلغ المسدد إذا كان موجودًا
                    paid_amount = 0.0
                    if 'Paid Amount' in row and not pd.isna(row['Paid Amount']):
                        paid_amount = float(row['Paid Amount'])
                    
                    # البحث عن الطالب في قاعدة البيانات
                    existing_debt = db.query(Debt).filter(Debt.student_name == student_name).first()
                    
                    if existing_debt:
                        # السجل موجود بالفعل
                        if update_existing:
                            # تحديث السجل الموجود
                            old_debt = existing_debt.debt_amount
                            old_paid = existing_debt.paid_amount
                            
                            existing_debt.debt_amount = debt_amount
                            existing_debt.paid_amount = paid_amount
                            
                            # إضافة سجل معاملة للتغيير في الدين إذا كان هناك اختلاف
                            if debt_amount != old_debt:
                                diff_amount = debt_amount - old_debt
                                new_transaction = Transaction(
                                    student_id=existing_debt.id,
                                    transaction_type="debt",
                                    amount=diff_amount,
                                    transaction_date=import_date,
                                    notes="تم تحديث القيمة من خلال استيراد ملف Excel"
                                )
                                db.add(new_transaction)
                            
                            # إضافة سجل معاملة للتغيير في المبلغ المسدد إذا كان هناك اختلاف
                            if paid_amount != old_paid:
                                diff_amount = paid_amount - old_paid
                                payment_transaction = Transaction(
                                    student_id=existing_debt.id,
                                    transaction_type="payment",
                                    amount=diff_amount,
                                    transaction_date=import_date,
                                    notes="تم تحديث القيمة من خلال استيراد ملف Excel"
                                )
                                db.add(payment_transaction)
                                
                            updated_count += 1
                        else:
                            # تخطي السجل الموجود
                            skipped_count += 1
                            continue
                    else:
                        # إنشاء سجل جديد
                        new_debt = Debt(
                            student_name=student_name,
                            debt_amount=debt_amount,
                            paid_amount=paid_amount
                        )
                        db.add(new_debt)
                        db.flush()  # للحصول على معرف السجل الجديد
                        
                        # إضافة سجل معاملة للدين الأولي
                        if debt_amount > 0:
                            new_transaction = Transaction(
                                student_id=new_debt.id,
                                transaction_type="debt",
                                amount=debt_amount,
                                transaction_date=import_date,
                                notes="تم استيراد من ملف إكسل"
                            )
                            db.add(new_transaction)
                        
                        # إضافة سجل معاملة للمبلغ المسدد إذا كان موجودًا
                        if paid_amount > 0:
                            payment_transaction = Transaction(
                                student_id=new_debt.id,
                                transaction_type="payment",
                                amount=paid_amount,
                                transaction_date=import_date,
                                notes="تم استيراد من ملف إكسل"
                            )
                            db.add(payment_transaction)
                            
                        added_count += 1
                        
                except Exception as row_error:
                    print(f"خطأ في استيراد صف واحد: {row_error}")
                    # متابعة للصف التالي دون التوقف
                    error_count += 1
                    continue
            
            db.commit()
            print(f"تم الاستيراد بنجاح: {added_count} سجل جديد، {updated_count} سجل محدث، {skipped_count} سجل متخطى، {error_count} سجل به خطأ")
            db.close()
            return added_count > 0 or updated_count > 0  # النجاح إذا تم إضافة أو تحديث سجل واحد على الأقل
        except Exception as e:
            print(f"خطأ في استيراد من DataFrame: {e}")
            if db:
                db.rollback()
                db.close()
            return False
    
    def import_from_excel(self, file, use_specific_format=False, names_col="C", debts_col="D", start_row=2, end_row=None, replace_existing=False, update_existing=True):
        """
        استيراد سجلات الديون من ملف Excel
        
        Args:
            file: كائن ملف أو مسار لملف Excel
            use_specific_format: استخدام تنسيق محدد حيث الأسماء والمديونيات في أعمدة محددة بدءاً من صف معين
            names_col: العمود الذي يحتوي على أسماء الطلاب (مثل "C")
            debts_col: العمود الذي يحتوي على مبالغ المديونية (مثل "D")
            start_row: رقم الصف البادئ (عادة 2 للبدء من الصف الثاني)
            end_row: رقم الصف النهائي (اختياري، إذا تم تحديده سيتم استيراد البيانات حتى هذا الصف فقط)
            replace_existing: حذف جميع السجلات الحالية قبل الاستيراد
            update_existing: تحديث السجلات الموجودة بدلاً من تخطيها
            
        Returns:
            bool: True إذا نجحت العملية، False خلاف ذلك
        """
        try:
            print(f"استيراد ملف: {file}")
            print(f"استخدام تنسيق محدد: {use_specific_format}")
            if use_specific_format:
                print(f"عمود الأسماء: {names_col}, عمود المديونيات: {debts_col}, بدء من الصف: {start_row}")
            
            # قراءة ملف Excel مع تحديد محرك openpyxl صراحة
            try:
                # محاولة قراءة الملف باستخدام محرك openpyxl
                if use_specific_format:
                    # قراءة الملف بدون تحديد أسماء الأعمدة
                    imported_df = pd.read_excel(file, engine='openpyxl', header=None)
                    print(f"تم قراءة الملف بدون أسماء أعمدة، شكل الملف: {imported_df.shape}")
                else:
                    imported_df = pd.read_excel(file, engine='openpyxl')
            except Exception as excel_error:
                print(f"خطأ في قراءة الملف باستخدام openpyxl: {excel_error}")
                # محاولة بديلة لقراءة الملف باستخدام محرك xlrd
                try:
                    if use_specific_format:
                        imported_df = pd.read_excel(file, engine='xlrd', header=None)
                    else:
                        imported_df = pd.read_excel(file, engine='xlrd')
                except Exception as xlrd_error:
                    print(f"خطأ في قراءة الملف باستخدام xlrd: {xlrd_error}")
                    return False
            
            # استخدام التنسيق المحدد حيث الأسماء في العمود C والمديونيات في العمود D
            if use_specific_format:
                # تحويل أسماء الأعمدة من حرف إلى رقم (مثل C -> 2, D -> 3)
                names_col_idx = ord(names_col.upper()) - ord('A')
                debts_col_idx = ord(debts_col.upper()) - ord('A')
                
                # طباعة معلومات تصحيح الأخطاء
                print(f"شكل ملف Excel: {imported_df.shape}")
                print(f"مؤشر عمود الأسماء: {names_col_idx}, مؤشر عمود المديونيات: {debts_col_idx}")
                
                # التحقق من وجود الأعمدة المطلوبة
                if names_col_idx >= imported_df.shape[1] or debts_col_idx >= imported_df.shape[1]:
                    print(f"خطأ: الأعمدة المحددة غير موجودة في الملف. عدد الأعمدة المتاحة: {imported_df.shape[1]}")
                    return False
                
                # إنشاء DataFrame جديد من البيانات المستخرجة
                start_row_idx = start_row - 1  # بما أن الفهرسة تبدأ من 0
                
                # استخراج الصفوف التي بها بيانات
                valid_rows = []
                
                # تحديد نطاق الصفوف للمعالجة
                if end_row is not None:
                    end_row_idx = min(end_row, len(imported_df))  # التأكد من عدم تجاوز عدد الصفوف المتاحة
                    print(f"تحديد الصفوف من {start_row} إلى {end_row} (مؤشر: {start_row_idx} إلى {end_row_idx-1})")
                else:
                    end_row_idx = len(imported_df)
                    print(f"معالجة جميع الصفوف من {start_row} إلى النهاية")
                
                for i in range(start_row_idx, end_row_idx):
                    name = imported_df.iloc[i, names_col_idx]
                    debt = imported_df.iloc[i, debts_col_idx]
                    
                    # تخطي الصفوف التي لا تحتوي على بيانات صالحة
                    if pd.notna(name) and pd.notna(debt):
                        valid_rows.append({
                            'Student Name': str(name).strip(),
                            'Debt Amount': float(debt) if isinstance(debt, (int, float)) else 0,
                            'Paid Amount': 0  # قيمة افتراضية للمبلغ المسدد
                        })
                
                if not valid_rows:
                    print("لم يتم العثور على بيانات صالحة في الملف")
                    return False
                
                # إنشاء DataFrame من البيانات المستخرجة
                new_df = pd.DataFrame(valid_rows)
                print(f"تم استخراج {len(new_df)} من السجلات من التنسيق المحدد")
                
            else:
                # استخدام التنسيق العادي مع اكتشاف الأعمدة تلقائياً
                
                # طباعة معلومات تصحيح الأخطاء
                print(f"أعمدة ملف Excel التي تم قراءتها: {imported_df.columns.tolist()}")
                print(f"أنواع البيانات: {imported_df.dtypes}")
                print(f"عدد الصفوف: {len(imported_df)}")
                
                # محاولة اكتشاف أعمدة البيانات تلقائيًا
                column_mappings = [
                    # قائمة من التعيينات المحتملة لأعمدة الاسم
                    {
                        'Student Name (اسم الطالب - مطلوب)': 'Student Name',
                        'اسم الطالب': 'Student Name',
                        'الاسم': 'Student Name',
                        'اسم': 'Student Name',
                        'الطالب': 'Student Name',
                        'Student Name': 'Student Name',
                        'Name': 'Student Name'
                    },
                # قائمة من التعيينات المحتملة لأعمدة الدين
                {
                    'Debt Amount (مبلغ الدين بالفلس - مطلوب)': 'Debt Amount',
                    'مبلغ الدين': 'Debt Amount',
                    'الدين': 'Debt Amount',
                    'Debt Amount (مبلغ الدين - مطلوب)': 'Debt Amount',
                    'Debt Amount': 'Debt Amount',
                    'Debt': 'Debt Amount',
                    'المدين': 'Debt Amount'
                },
                # قائمة من التعيينات المحتملة لأعمدة المسدد
                {
                    'Paid Amount (المبلغ المسدد بالفلس - اختياري)': 'Paid Amount',
                    'المبلغ المسدد': 'Paid Amount',
                    'المسدد': 'Paid Amount',
                    'Paid Amount (المبلغ المسدد - اختياري)': 'Paid Amount',
                    'Paid Amount': 'Paid Amount',
                    'Paid': 'Paid Amount',
                    'المسدد': 'Paid Amount'
                }
            ]
            
            # تعريف محلي لمسح الأعمدة وإعادة تسميتها - فقط للتنسيق العادي
            if 'column_mappings' in locals():
                for mapping in column_mappings:
                    for source_col, target_col in mapping.items():
                        # البحث عن أي عمود يحتوي على نص مشابه
                        for col in imported_df.columns:
                            # التحقق من أن العمود هو نص وليس رقم
                            if isinstance(col, str) and isinstance(source_col, str):
                                if source_col in col or col in source_col:
                                    imported_df = imported_df.rename(columns={col: target_col})
                                    break
                            # إذا كان اسم العمود هو "الاسم" أو "اسم الطالب"، فقم بالتعيين
                            elif isinstance(col, str) and col == "الاسم":
                                imported_df = imported_df.rename(columns={col: "Student Name"})
                                break
                            # إذا كان اسم العمود هو "المجموع"، فقم بتعيينه كمبلغ الدين
                            elif isinstance(col, str) and col == "المجموع":
                                imported_df = imported_df.rename(columns={col: "Debt Amount"})
                                break
            
            # التحقق من الأعمدة الإلزامية
            required_columns = ['Student Name', 'Debt Amount']
            
            # محاولة التعرف على الأعمدة من المحتوى إذا لم يتم العثور على الأعمدة المطلوبة
            if not all(col in imported_df.columns for col in required_columns):
                # إذا كان لدينا عمودين على الأقل، يمكن أن نفترض أن العمود الأول هو الاسم والثاني هو الدين
                if len(imported_df.columns) >= 2:
                    col_list = imported_df.columns.tolist()
                    renamed_cols = {}
                    renamed_cols[col_list[0]] = 'Student Name'
                    renamed_cols[col_list[1]] = 'Debt Amount'
                    if len(col_list) >= 3:
                        renamed_cols[col_list[2]] = 'Paid Amount'
                    imported_df = imported_df.rename(columns=renamed_cols)
                else:
                    return False
            
            # تحديد الأعمدة التي سيتم الاحتفاظ بها
            columns_to_keep = required_columns.copy()
            
            # إضافة عمود المبلغ المسدد إذا كان موجودًا
            if 'Paid Amount' in imported_df.columns:
                columns_to_keep.append('Paid Amount')
                
                # التحقق من نوع البيانات في عمود المبلغ المسدد
                if not pd.api.types.is_numeric_dtype(imported_df['Paid Amount']):
                    # محاولة التحويل إلى رقمي، مع تحويل الأخطاء إلى 0
                    imported_df['Paid Amount'] = pd.to_numeric(imported_df['Paid Amount'], errors='coerce').fillna(0)
            else:
                # إضافة عمود فارغ للمبلغ المسدد
                imported_df['Paid Amount'] = 0
                columns_to_keep.append('Paid Amount')
            
            # الاحتفاظ بالأعمدة المطلوبة فقط
            imported_df = imported_df[columns_to_keep]
            
            # التحقق من أنواع البيانات في عمود قيمة الدين
            if not pd.api.types.is_numeric_dtype(imported_df['Debt Amount']):
                # محاولة التحويل إلى رقمي، مع تحويل الأخطاء إلى NaN
                imported_df['Debt Amount'] = pd.to_numeric(imported_df['Debt Amount'], errors='coerce')
                # حذف الصفوف التي تحتوي على NaN في مبالغ الدين
                imported_df = imported_df.dropna(subset=['Debt Amount'])
            
            # تحويل القيم من فلس إلى دينار للتخزين في قاعدة البيانات
            imported_df['Debt Amount'] = imported_df['Debt Amount'] / 1000
            if 'Paid Amount' in imported_df.columns:
                imported_df['Paid Amount'] = imported_df['Paid Amount'] / 1000
            
            # التأكد من عدم وجود قيم فارغة في أسماء الطلاب
            imported_df = imported_df.dropna(subset=['Student Name'])
            
            # استيراد البيانات إلى قاعدة البيانات مع تمرير خيارات التحديث والاستبدال
            return self.import_from_dataframe(imported_df, replace_existing, update_existing)
        except Exception as e:
            print(f"خطأ في استيراد من Excel: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def export_to_excel(self, file_path):
        """
        تصدير سجلات الديون إلى ملف Excel
        
        Args:
            file_path (str): مسار لحفظ ملف Excel
            
        Returns:
            bool: True إذا نجحت العملية، False خلاف ذلك
        """
        try:
            # الحصول على DataFrame
            df = self.get_dataframe()
            
            # تحويل المبالغ من دينار إلى فلس للتصدير
            export_df = df.copy()
            for col in ['Debt Amount', 'Paid Amount', 'Remaining Amount']:
                if col in export_df.columns:
                    export_df[col] = export_df[col] * 1000
            
            # إعادة تسمية الأعمدة لتوضيح أن المبالغ بالفلس
            column_mapping = {
                'Student Name': 'Student Name (اسم الطالب)',
                'Debt Amount': 'Debt Amount (مبلغ الدين بالفلس)',
                'Paid Amount': 'Paid Amount (المبلغ المسدد بالفلس)',
                'Remaining Amount': 'Remaining Amount (المبلغ المتبقي بالفلس)'
            }
            export_df = export_df.rename(columns=column_mapping)
            
            # تصدير إلى Excel
            export_df.to_excel(file_path, index=False)
            return True
        except Exception as e:
            print(f"خطأ في التصدير إلى Excel: {e}")
            return False
    
    def get_payment_status_report(self):
        """
        الحصول على تقرير حالة السداد للطلاب.
        يقوم بتصنيف الطلاب إلى فئات حسب حالة السداد الخاصة بهم.
        
        Returns:
            tuple: (fully_paid_df, partially_paid_df, unpaid_df) - إطارات بيانات للطلاب المسددين بالكامل، المسددين جزئياً، وغير المسددين
        """
        try:
            # الحصول على البيانات الكاملة
            df = self.get_dataframe()
            
            if df.empty:
                # إذا لم تكن هناك بيانات، أرجع إطارات بيانات فارغة
                empty_df = pd.DataFrame(columns=['Student Name', 'Debt Amount', 'Paid Amount', 'Remaining Amount', 'Payment Percentage'])
                return empty_df, empty_df, empty_df
            
            # إضافة نسبة السداد
            df['Payment Percentage'] = (df['Paid Amount'] / df['Debt Amount'] * 100).fillna(0).round(1)
            
            # تصنيف الطلاب
            fully_paid = df[df['Remaining Amount'] == 0].copy()  # المسددين بالكامل
            partially_paid = df[(df['Paid Amount'] > 0) & (df['Remaining Amount'] > 0)].copy()  # المسددين جزئياً
            unpaid = df[df['Paid Amount'] == 0].copy()  # غير المسددين
            
            # ترتيب البيانات
            fully_paid = fully_paid.sort_values(by='Debt Amount', ascending=False)
            partially_paid = partially_paid.sort_values(by='Payment Percentage', ascending=False)
            unpaid = unpaid.sort_values(by='Debt Amount', ascending=False)
            
            return fully_paid, partially_paid, unpaid
            
        except Exception as e:
            print(f"خطأ في الحصول على تقرير حالة السداد: {e}")
            empty_df = pd.DataFrame(columns=['Student Name', 'Debt Amount', 'Paid Amount', 'Remaining Amount', 'Payment Percentage'])
            return empty_df, empty_df, empty_df

    def get_student_transactions(self, student_name):
        """
        الحصول على سجل معاملات لطالب معين
        
        Args:
            student_name (str): اسم الطالب
            
        Returns:
            DataFrame: إطار بيانات يحتوي على سجل المعاملات، أو DataFrame فارغ إذا لم يوجد الطالب
        """
        db = None
        try:
            db = self.SessionLocal()
            
            # البحث عن سجل الدين
            debt = db.query(Debt).filter(Debt.student_name == student_name).first()
            if not debt:
                db.close()
                return pd.DataFrame(columns=['Transaction Type', 'Amount', 'Transaction Date', 'Notes'])
            
            # الحصول على معاملات الطالب
            transactions = db.query(Transaction).filter(Transaction.student_id == debt.id).all()
            db.close()
            
            # إنشاء DataFrame من المعاملات
            if transactions:
                data = {
                    'Transaction Type': [
                        'إضافة دين' if t.transaction_type == 'debt' else 'تسديد دين' 
                        for t in transactions
                    ],
                    'Amount': [t.amount for t in transactions],
                    'Transaction Date': [t.transaction_date for t in transactions],
                    'Notes': [t.notes for t in transactions]
                }
                return pd.DataFrame(data)
            else:
                return pd.DataFrame(columns=['Transaction Type', 'Amount', 'Transaction Date', 'Notes'])
        except Exception as e:
            print(f"خطأ في الحصول على سجل المعاملات: {e}")
            if db:
                db.close()
            return pd.DataFrame(columns=['Transaction Type', 'Amount', 'Transaction Date', 'Notes'])