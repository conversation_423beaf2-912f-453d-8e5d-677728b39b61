# نشر النظام على الويب

هذا الدليل يشرح كيفية نشر نظام إدارة ديون الطلاب على الويب باستخدام خادم Nginx كوسيط عكسي.

## المتطلبات
- خادم يعمل بنظام تشغيل Linux (Ubuntu أو Debian مفضل)
- تثبيت Nginx
- تثبيت نظام إدارة ديون الطلاب بنجاح

## خطوات النشر

### 1. إعداد النظام كخدمة systemd

1. أنشئ ملف خدمة systemd:
```bash
sudo nano /etc/systemd/system/student-debt.service
```

2. أضف المحتوى التالي (قم بتعديل المسارات حسب إعدادك):
```
[Unit]
Description=Student Debt Management System
After=network.target

[Service]
User=your_username
WorkingDirectory=/path/to/student_debt_system
ExecStart=/usr/bin/python3 -m streamlit run app.py --server.port 8501 --server.address 0.0.0.0
Restart=always
StandardOutput=syslog
StandardError=syslog
SyslogIdentifier=student-debt
Environment="PATH=/path/to/python/venv/bin:/usr/local/bin:/usr/bin:/bin"
Environment="DATABASE_URL=postgresql://username:password@localhost:5432/student_debts"
Environment="PGUSER=username"
Environment="PGPASSWORD=password"
Environment="PGDATABASE=student_debts"
Environment="PGHOST=localhost"
Environment="PGPORT=5432"

[Install]
WantedBy=multi-user.target
```

3. تفعيل وتشغيل الخدمة:
```bash
sudo systemctl daemon-reload
sudo systemctl enable student-debt
sudo systemctl start student-debt
```

4. التحقق من حالة الخدمة:
```bash
sudo systemctl status student-debt
```

### 2. تثبيت وإعداد Nginx

1. تثبيت Nginx:
```bash
sudo apt update
sudo apt install nginx
```

2. إنشاء ملف إعدادات للموقع:
```bash
sudo nano /etc/nginx/sites-available/student-debt
```

3. إضافة المحتوى التالي (قم بتعديل اسم النطاق حسب احتياجاتك):
```
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;

    access_log /var/log/nginx/student-debt.access.log;
    error_log /var/log/nginx/student-debt.error.log;

    location / {
        proxy_pass http://localhost:8501;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_buffering off;
    }

    # Static files specific to Streamlit
    location ^~ /static {
        proxy_pass http://localhost:8501/static/;
    }

    location ^~ /healthz {
        proxy_pass http://localhost:8501/healthz;
    }

    location ^~ /vendor {
        proxy_pass http://localhost:8501/vendor;
    }

    location ~ ^/(.+)/(stream|index|favicon|manifest|favicon-32|favicon-16) {
        proxy_pass http://localhost:8501/$1/$2;
    }
}
```

4. تفعيل موقع Nginx:
```bash
sudo ln -s /etc/nginx/sites-available/student-debt /etc/nginx/sites-enabled/
sudo nginx -t  # للتحقق من صحة الإعدادات
sudo systemctl restart nginx
```

### 3. إعداد HTTPS (اختياري ولكن موصى به)

1. تثبيت Certbot:
```bash
sudo apt install certbot python3-certbot-nginx
```

2. الحصول على شهادة SSL:
```bash
sudo certbot --nginx -d your-domain.com -d www.your-domain.com
```

3. اتبع التعليمات التي تظهر لإكمال إعداد HTTPS.

4. التحقق من التجديد التلقائي للشهادة:
```bash
sudo certbot renew --dry-run
```

## فتح المنافذ في جدار الحماية

إذا كان لديك جدار حماية مفعل، ستحتاج إلى فتح المنافذ 80 (HTTP) و443 (HTTPS):

```bash
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
```

## اختبار الوصول

بعد إكمال الإعدادات، يمكنك الوصول إلى النظام عبر المتصفح باستخدام عنوان النطاق الخاص بك:

```
http://your-domain.com
```

أو إذا قمت بإعداد HTTPS:

```
https://your-domain.com
```

## حل المشكلات الشائعة

### مشكلة: لا يمكن الوصول إلى الموقع

1. تحقق من حالة الخدمة:
```bash
sudo systemctl status student-debt
```

2. تحقق من سجلات الخدمة:
```bash
sudo journalctl -u student-debt
```

3. تحقق من سجلات Nginx:
```bash
sudo tail -f /var/log/nginx/student-debt.error.log
```

### مشكلة: خطأ في الاتصال بقاعدة البيانات

1. تحقق من سجلات الخدمة:
```bash
sudo journalctl -u student-debt
```

2. تأكد من صحة متغيرات البيئة في ملف خدمة systemd.

### مشكلة: "502 Bad Gateway"

1. تأكد من أن تطبيق Streamlit يعمل:
```bash
sudo systemctl status student-debt
```

2. تحقق من إعدادات Nginx:
```bash
sudo nginx -t
```

## النسخ الاحتياطي والصيانة

### النسخ الاحتياطي الدوري لقاعدة البيانات

إنشاء سكريبت للنسخ الاحتياطي:

```bash
sudo nano /usr/local/bin/backup_student_debt_db.sh
```

أضف المحتوى التالي:
```bash
#!/bin/bash
BACKUP_DIR="/path/to/backups"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
FILENAME="student_debt_backup_${TIMESTAMP}.sql"

# إنشاء دليل النسخ الاحتياطي إذا لم يكن موجودًا
mkdir -p $BACKUP_DIR

# عمل نسخة احتياطية
pg_dump -U username -d student_debts > "$BACKUP_DIR/$FILENAME"

# ضغط الملف
gzip "$BACKUP_DIR/$FILENAME"

# حذف النسخ الاحتياطية القديمة (أكثر من 30 يومًا)
find $BACKUP_DIR -name "student_debt_backup_*.sql.gz" -type f -mtime +30 -delete
```

اجعل السكريبت قابل للتنفيذ وأضفه إلى cron:
```bash
sudo chmod +x /usr/local/bin/backup_student_debt_db.sh
sudo crontab -e
```

أضف هذا السطر للتشغيل اليومي في الساعة 3 صباحًا:
```
0 3 * * * /usr/local/bin/backup_student_debt_db.sh
```

## الاستنتاج

باتباع هذه الخطوات، ستكون قد نشرت نظام إدارة ديون الطلاب على خادم ويب مع إمكانية الوصول إليه من خلال نطاق خاص بك وإعداد HTTPS. 

تأكد من الحفاظ على أمان الخادم وعمل نسخ احتياطية دورية لضمان سلامة البيانات.