"""
وحدة لإنشاء وتصدير تقارير PDF بشكل موثوق
"""
import os
import base64
import tempfile
from datetime import datetime
import io
import pandas as pd
from fpdf import FPDF
import streamlit as st

from utils import format_currency

class SimplePDF(FPDF):
    """فئة معدلة من FPDF تستخدم بشكل أساسي اللغة الإنجليزية مع ترجمة للمصطلحات الرئيسية"""
    
    def __init__(self):
        # استخدام UTF-8 لدعم النصوص العربية، لكن مع Unicode نسخة مبسطة
        super().__init__()
        # تعيين الترميز لتجنب مشاكل مع الأحرف العربية
        self.encoding = 'utf-8'
    
    def header(self):
        """ترويسة الصفحة"""
        # شعار أو عنوان الصفحة
        self.set_font('Arial', 'B', 15)
        self.cell(0, 10, 'Student Debt Management System', 0, 1, 'C')
        self.ln(5)
    
    def footer(self):
        """تذييل الصفحة"""
        # الانتقال إلى موضع التذييل
        self.set_y(-15)
        self.set_font('Arial', 'I', 8)
        # رقم الصفحة
        self.cell(0, 10, f'Page {self.page_no()}', 0, 0, 'C')
        # التاريخ
        self.cell(0, 10, datetime.now().strftime('%Y-%m-%d'), 0, 0, 'R')


def create_pdf_buffer(content_function):
    """
    دالة مساعدة لإنشاء كائن ذاكرة PDF بطريقة آمنة
    
    Args:
        content_function: دالة تقوم بإضافة المحتوى إلى كائن PDF
        
    Returns:
        BytesIO: كائن ذاكرة يحتوي على ملف PDF
    """
    try:
        # إنشاء ملف مؤقت
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as tmp:
            tmp_path = tmp.name
        
        # إنشاء وحفظ PDF
        pdf = content_function()
        pdf.output(tmp_path)
        
        # قراءة الملف المؤقت إلى كائن ذاكرة
        with open(tmp_path, 'rb') as f:
            buffer = io.BytesIO(f.read())
        
        # حذف الملف المؤقت
        os.unlink(tmp_path)
        
        # إعادة ضبط مؤشر كائن الذاكرة إلى البداية
        buffer.seek(0)
        
        return buffer
        
    except Exception as e:
        # إنشاء PDF خطأ
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as tmp:
            tmp_path = tmp.name
        
        error_pdf = SimplePDF()
        error_pdf.add_page()
        error_pdf.set_font('Arial', 'B', 16)
        error_pdf.cell(0, 10, "Error Generating PDF Report", 0, 1, 'C')
        error_pdf.ln(10)
        error_pdf.set_font('Arial', '', 12)
        error_pdf.multi_cell(0, 10, f"An error occurred while generating the PDF report: {str(e)}\n\nPlease view the data in the application instead.", 0, 'L')
        
        error_pdf.output(tmp_path)
        
        # قراءة الملف المؤقت إلى كائن ذاكرة
        with open(tmp_path, 'rb') as f:
            buffer = io.BytesIO(f.read())
        
        # حذف الملف المؤقت
        os.unlink(tmp_path)
        
        # إعادة ضبط مؤشر كائن الذاكرة إلى البداية
        buffer.seek(0)
        
        return buffer


def export_all_students_pdf(df):
    """
    تصدير بيانات جميع الطلاب إلى ملف PDF
    
    Args:
        df (DataFrame): بيانات الطلاب
        
    Returns:
        BytesIO: كائن ذاكرة يحتوي على ملف PDF
    """
    def create_content():
        # إنشاء ملف PDF جديد
        pdf = SimplePDF()
        pdf.add_page()
        
        # عنوان التقرير
        pdf.set_font('Arial', 'B', 16)
        pdf.cell(0, 10, 'All Students Debt Report', 0, 1, 'C')
        pdf.ln(10)
        
        # البيانات الإحصائية
        pdf.set_font('Arial', 'B', 12)
        pdf.cell(0, 10, 'Summary Statistics', 0, 1, 'L')
        
        # القيم الإحصائية
        pdf.set_font('Arial', '', 10)
        total_students = len(df)
        total_debt = df['Debt Amount'].sum()
        total_paid = df['Paid Amount'].sum()
        total_remaining = df['Remaining Amount'].sum()
        payment_ratio = (total_paid / total_debt * 100) if total_debt > 0 else 0
        
        pdf.cell(60, 8, 'Total Students:', 0, 0)
        pdf.cell(0, 8, f'{total_students}', 0, 1)
        
        pdf.cell(60, 8, 'Total Debt:', 0, 0)
        pdf.cell(0, 8, f'{format_currency(total_debt)}', 0, 1)
        
        pdf.cell(60, 8, 'Total Paid:', 0, 0)
        pdf.cell(0, 8, f'{format_currency(total_paid)}', 0, 1)
        
        pdf.cell(60, 8, 'Total Remaining:', 0, 0)
        pdf.cell(0, 8, f'{format_currency(total_remaining)}', 0, 1)
        
        pdf.cell(60, 8, 'Payment Ratio:', 0, 0)
        pdf.cell(0, 8, f'{payment_ratio:.1f}%', 0, 1)
        
        pdf.ln(10)
        
        # جدول بيانات الطلاب
        pdf.set_font('Arial', 'B', 12)
        pdf.cell(0, 10, 'Student Records', 0, 1, 'L')
        
        # رؤوس الجدول
        pdf.set_font('Arial', 'B', 10)
        pdf.cell(60, 8, 'Student Name', 1, 0, 'C')
        pdf.cell(40, 8, 'Debt Amount', 1, 0, 'C')
        pdf.cell(40, 8, 'Paid Amount', 1, 0, 'C')
        pdf.cell(40, 8, 'Remaining', 1, 1, 'C')
        
        # بيانات الجدول
        pdf.set_font('Arial', '', 10)
        for i, row in df.iterrows():
            # التعامل مع أسماء الطلاب غير الإنجليزية
            # استبدال الأحرف غير اللاتينية بـ "Student #ID" لتجنب مشاكل الترميز
            if any(ord(c) > 127 for c in row['Student Name']):
                safe_name = f"Student #{i+1}"
            else:
                safe_name = row['Student Name']
            
            pdf.cell(60, 8, safe_name, 1, 0)
            pdf.cell(40, 8, format_currency(row['Debt Amount']), 1, 0, 'R')
            pdf.cell(40, 8, format_currency(row['Paid Amount']), 1, 0, 'R')
            pdf.cell(40, 8, format_currency(row['Remaining Amount']), 1, 1, 'R')
        
        # ملاحظة عن النص العربي
        pdf.ln(10)
        pdf.set_font('Arial', 'B', 12)
        pdf.cell(0, 10, 'Note About Arabic Text', 0, 1, 'C')
        pdf.ln(5)
        pdf.set_font('Arial', '', 10)
        pdf.multi_cell(0, 8, "This report may not display Arabic names correctly due to PDF encoding limitations. Please use the online application for full Arabic text support.", 0, 'L')
        
        return pdf
    
    return create_pdf_buffer(create_content)


def export_student_report_pdf(student_name, debt_data, transactions_df):
    """
    تصدير تقرير تفصيلي لطالب معين إلى ملف PDF
    
    Args:
        student_name (str): اسم الطالب
        debt_data (dict): بيانات دين الطالب
        transactions_df (DataFrame): سجل معاملات الطالب
        
    Returns:
        BytesIO: كائن ذاكرة يحتوي على ملف PDF
    """
    def create_content():
        # إنشاء ملف PDF جديد
        pdf = SimplePDF()
        pdf.add_page()
        
        # عنوان التقرير
        pdf.set_font('Arial', 'B', 16)
        
        # التعامل مع أسماء الطلاب غير الإنجليزية
        if any(ord(c) > 127 for c in student_name):
            report_title = "Student Report"
        else:
            report_title = f"Report: {student_name}"
            
        pdf.cell(0, 10, report_title, 0, 1, 'C')
        pdf.ln(10)
        
        # ملخص بيانات الطالب
        pdf.set_font('Arial', 'B', 12)
        pdf.cell(0, 10, 'Student Information', 0, 1, 'L')
        
        # القيم المالية للطالب
        pdf.set_font('Arial', '', 10)
        
        pdf.cell(60, 8, 'Debt Amount:', 0, 0)
        pdf.cell(0, 8, f'{format_currency(debt_data["debt_amount"])}', 0, 1)
        
        pdf.cell(60, 8, 'Paid Amount:', 0, 0)
        pdf.cell(0, 8, f'{format_currency(debt_data["paid_amount"])}', 0, 1)
        
        remaining_amount = debt_data.get("remaining_amount", debt_data["debt_amount"] - debt_data["paid_amount"])
        pdf.cell(60, 8, 'Remaining Amount:', 0, 0)
        pdf.cell(0, 8, f'{format_currency(remaining_amount)}', 0, 1)
        
        payment_percentage = debt_data.get("payment_percentage", 
                                     (debt_data["paid_amount"] / debt_data["debt_amount"] * 100) if debt_data["debt_amount"] > 0 else 0)
        pdf.cell(60, 8, 'Payment Percentage:', 0, 0)
        pdf.cell(0, 8, f'{payment_percentage:.1f}%', 0, 1)
        
        pdf.ln(10)
        
        # سجل المعاملات
        pdf.set_font('Arial', 'B', 12)
        pdf.cell(0, 10, 'Transaction History', 0, 1, 'L')
        
        if transactions_df.empty:
            pdf.set_font('Arial', '', 10)
            pdf.cell(0, 8, 'No transactions recorded for this student.', 0, 1)
        else:
            # رؤوس جدول المعاملات
            pdf.set_font('Arial', 'B', 10)
            pdf.cell(40, 8, 'Date', 1, 0, 'C')
            pdf.cell(30, 8, 'Type', 1, 0, 'C')
            pdf.cell(40, 8, 'Amount', 1, 0, 'C')
            pdf.cell(70, 8, 'Notes', 1, 1, 'C')
            
            # بيانات جدول المعاملات
            pdf.set_font('Arial', '', 10)
            
            # تحقق من أسماء الأعمدة المتوقعة
            date_col = 'Transaction Date' if 'Transaction Date' in transactions_df.columns else 'Date'
            type_col = 'Transaction Type' if 'Transaction Type' in transactions_df.columns else 'Type'
            notes_col = 'Notes' if 'Notes' in transactions_df.columns else None
            
            for i, row in transactions_df.iterrows():
                transaction_type = "Payment" if row[type_col].lower() == 'payment' else "Debt"
                
                # تنسيق التاريخ
                date_val = row[date_col]
                date_str = date_val.strftime('%Y-%m-%d') if hasattr(date_val, 'strftime') else str(date_val)
                
                # التعامل مع الملاحظات - استبدال النص العربي إذا وجد
                notes = row[notes_col] if notes_col and isinstance(row[notes_col], str) else ""
                if any(ord(c) > 127 for c in notes):
                    notes = "Transaction notes (Arabic text)"
                
                pdf.cell(40, 8, date_str, 1, 0)
                pdf.cell(30, 8, transaction_type, 1, 0)
                pdf.cell(40, 8, format_currency(row['Amount']), 1, 0, 'R')
                pdf.cell(70, 8, notes[:25] + "..." if len(notes) > 25 else notes, 1, 1)
        
        # ملاحظة عن النص العربي
        pdf.ln(10)
        pdf.set_font('Arial', 'B', 12)
        pdf.cell(0, 10, 'Note About Arabic Text', 0, 1, 'C')
        pdf.ln(5)
        pdf.set_font('Arial', '', 10)
        pdf.multi_cell(0, 8, "This report may not display Arabic text correctly due to PDF encoding limitations. Please use the online application for full Arabic text support.", 0, 'L')
        
        return pdf
    
    return create_pdf_buffer(create_content)


def export_payment_status_report_pdf(fully_paid_df, partially_paid_df, unpaid_df):
    """
    تصدير تقرير حالة السداد للطلاب إلى ملف PDF.
    يعرض قوائم للطلاب المسددين بالكامل، المسددين جزئياً، وغير المسددين
    
    Args:
        fully_paid_df (DataFrame): إطار بيانات الطلاب المسددين بالكامل
        partially_paid_df (DataFrame): إطار بيانات الطلاب المسددين جزئياً
        unpaid_df (DataFrame): إطار بيانات الطلاب غير المسددين
        
    Returns:
        BytesIO: كائن ذاكرة يحتوي على ملف PDF
    """
    def create_content():
        # إنشاء ملف PDF جديد
        pdf = SimplePDF()
        pdf.add_page()
        
        # عنوان التقرير
        pdf.set_font('Arial', 'B', 16)
        pdf.cell(0, 10, 'Payment Status Report', 0, 1, 'C')
        pdf.ln(10)
        
        # ملخص
        pdf.set_font('Arial', 'B', 12)
        pdf.cell(0, 10, 'Summary', 0, 1, 'L')
        
        pdf.set_font('Arial', '', 10)
        pdf.cell(60, 8, 'Fully Paid Students:', 0, 0)
        pdf.cell(0, 8, f'{len(fully_paid_df)}', 0, 1)
        
        pdf.cell(60, 8, 'Partially Paid Students:', 0, 0)
        pdf.cell(0, 8, f'{len(partially_paid_df)}', 0, 1)
        
        pdf.cell(60, 8, 'Unpaid Students:', 0, 0)
        pdf.cell(0, 8, f'{len(unpaid_df)}', 0, 1)
        
        pdf.cell(60, 8, 'Total Students:', 0, 0)
        pdf.cell(0, 8, f'{len(fully_paid_df) + len(partially_paid_df) + len(unpaid_df)}', 0, 1)
        
        # الطلاب المسددين بالكامل
        if not fully_paid_df.empty:
            pdf.ln(10)
            pdf.set_font('Arial', 'B', 12)
            pdf.cell(0, 10, f'Fully Paid Students ({len(fully_paid_df)})', 0, 1, 'L')
            
            # رؤوس الجدول
            pdf.set_font('Arial', 'B', 10)
            pdf.cell(60, 8, 'Student Name', 1, 0, 'C')
            pdf.cell(40, 8, 'Debt Amount', 1, 0, 'C')
            pdf.cell(40, 8, 'Paid Amount', 1, 0, 'C')
            pdf.cell(40, 8, 'Remaining', 1, 1, 'C')
            
            # بيانات الجدول
            pdf.set_font('Arial', '', 10)
            for i, row in fully_paid_df.iterrows():
                # التعامل مع أسماء الطلاب غير الإنجليزية
                if any(ord(c) > 127 for c in row['Student Name']):
                    safe_name = f"Fully Paid Student #{i+1}"
                else:
                    safe_name = row['Student Name']
                
                pdf.cell(60, 8, safe_name, 1, 0)
                pdf.cell(40, 8, format_currency(row['Debt Amount']), 1, 0, 'R')
                pdf.cell(40, 8, format_currency(row['Paid Amount']), 1, 0, 'R')
                pdf.cell(40, 8, format_currency(row['Remaining Amount']), 1, 1, 'R')
        
        # الطلاب المسددين جزئياً
        if not partially_paid_df.empty:
            pdf.ln(10)
            pdf.set_font('Arial', 'B', 12)
            pdf.cell(0, 10, f'Partially Paid Students ({len(partially_paid_df)})', 0, 1, 'L')
            
            # رؤوس الجدول
            pdf.set_font('Arial', 'B', 10)
            pdf.cell(50, 8, 'Student Name', 1, 0, 'C')
            pdf.cell(35, 8, 'Debt Amount', 1, 0, 'C')
            pdf.cell(35, 8, 'Paid Amount', 1, 0, 'C')
            pdf.cell(35, 8, 'Remaining', 1, 0, 'C')
            pdf.cell(25, 8, 'Payment %', 1, 1, 'C')
            
            # بيانات الجدول
            pdf.set_font('Arial', '', 10)
            for i, row in partially_paid_df.iterrows():
                # التعامل مع أسماء الطلاب غير الإنجليزية
                if any(ord(c) > 127 for c in row['Student Name']):
                    safe_name = f"Partially Paid #{i+1}"
                else:
                    safe_name = row['Student Name']
                
                pdf.cell(50, 8, safe_name, 1, 0)
                pdf.cell(35, 8, format_currency(row['Debt Amount']), 1, 0, 'R')
                pdf.cell(35, 8, format_currency(row['Paid Amount']), 1, 0, 'R')
                pdf.cell(35, 8, format_currency(row['Remaining Amount']), 1, 0, 'R')
                pdf.cell(25, 8, f"{row['Payment Percentage']:.1f}%", 1, 1, 'R')
        
        # الطلاب غير المسددين
        if not unpaid_df.empty:
            pdf.ln(10)
            pdf.set_font('Arial', 'B', 12)
            pdf.cell(0, 10, f'Unpaid Students ({len(unpaid_df)})', 0, 1, 'L')
            
            # رؤوس الجدول
            pdf.set_font('Arial', 'B', 10)
            pdf.cell(60, 8, 'Student Name', 1, 0, 'C')
            pdf.cell(40, 8, 'Debt Amount', 1, 0, 'C')
            pdf.cell(40, 8, 'Paid Amount', 1, 0, 'C')
            pdf.cell(40, 8, 'Remaining', 1, 1, 'C')
            
            # بيانات الجدول
            pdf.set_font('Arial', '', 10)
            for i, row in unpaid_df.iterrows():
                # التعامل مع أسماء الطلاب غير الإنجليزية
                if any(ord(c) > 127 for c in row['Student Name']):
                    safe_name = f"Unpaid Student #{i+1}"
                else:
                    safe_name = row['Student Name']
                
                pdf.cell(60, 8, safe_name, 1, 0)
                pdf.cell(40, 8, format_currency(row['Debt Amount']), 1, 0, 'R')
                pdf.cell(40, 8, format_currency(row['Paid Amount']), 1, 0, 'R')
                pdf.cell(40, 8, format_currency(row['Remaining Amount']), 1, 1, 'R')
        
        # ملاحظة عن النص العربي
        pdf.ln(10)
        pdf.set_font('Arial', 'B', 12)
        pdf.cell(0, 10, 'Note About Arabic Text', 0, 1, 'C')
        pdf.ln(5)
        pdf.set_font('Arial', '', 10)
        pdf.multi_cell(0, 8, "This report may not display Arabic names correctly due to PDF encoding limitations. Please use the online application for full Arabic text support.", 0, 'L')
        
        return pdf
    
    return create_pdf_buffer(create_content)


def create_download_link(pdf_bytes, filename):
    """إنشاء رابط تنزيل للملف"""
    b64 = base64.b64encode(pdf_bytes.read()).decode()
    return f'<a href="data:application/octet-stream;base64,{b64}" download="{filename}">انقر هنا للتنزيل</a>'