import streamlit as st
import pandas as pd
import os
import datetime
import io
import base64
import plotly.express as px
import plotly.graph_objects as go
from database import DatabaseManager
from visualization import create_debt_visualization, create_debt_distribution_chart
from utils import validate_student_name, validate_debt_amount, format_currency, generate_excel_template
# استيراد وظائف التقرير البسيط (احتياطي)
from simple_report import create_all_students_report as create_all_students_report_simple
from simple_report import create_student_report as create_student_report_simple
from simple_report import create_payment_status_report as create_payment_status_report_simple
from simple_report import create_download_link

# استيراد وظائف التقرير المحسن
from enhanced_report import create_enhanced_all_students_report, create_enhanced_student_report, create_enhanced_payment_status_report

# تعيين وظائف التقرير الافتراضية
create_all_students_report = create_enhanced_all_students_report
create_student_report = create_enhanced_student_report
create_payment_status_report = create_enhanced_payment_status_report

# إعدادات الصفحة
st.set_page_config(
    page_title="نظام إدارة ديون الطلاب",
    page_icon="📚",
    layout="wide"
)

# تطبيق تنسيق CSS مخصص
st.markdown("""
<style>
    /* تنسيق عام */
    .main .block-container {
        padding-top: 2rem;
        padding-bottom: 2rem;
    }
    h1, h2, h3, h4, h5, h6 {
        color: #0078D4;
        margin-bottom: 1rem;
    }
    /* تنسيق البطاقات والأقسام */
    .dashboard-card {
        background-color: white;
        border-radius: 10px;
        padding: 1.5rem;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        margin-bottom: 1rem;
    }
    /* تنسيق الجداول */
    .dataframe {
        width: 100%;
        border-collapse: collapse;
    }
    .dataframe th {
        background-color: #f0f2f6;
        padding: 10px;
        text-align: center;
        font-weight: bold;
        border: 1px solid #ddd;
    }
    .dataframe td {
        padding: 8px;
        border: 1px solid #ddd;
        text-align: center;
    }
    .dataframe tr:hover {
        background-color: #f5f5f5;
    }
    /* تنسيق الأزرار */
    .stButton>button {
        font-weight: bold;
        border-radius: 6px;
        padding: 0.5rem 1rem;
        transition: all 0.2s;
    }
    .stButton>button:hover {
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        transform: translateY(-2px);
    }
    /* تنسيق الشريط الجانبي */
    .css-1d391kg {
        padding-top: 2rem;
    }
    .sidebar-student-button {
        margin-bottom: 5px;
        text-align: right;
        width: 100%;
    }
    /* تنسيق الفواصل */
    hr {
        margin: 2rem 0;
        border: none;
        height: 1px;
        background-color: #ddd;
    }
    /* لتحسين مظهر المقاييس */
    .stMetric {
        background-color: white;
        border-radius: 8px;
        padding: 10px;
        box-shadow: 0 1px 2px rgba(0,0,0,0.05);
    }
    /* لتحسين مظهر نماذج الإدخال */
    .stForm {
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 20px;
    }
    /* تنسيق للشاشات الصغيرة */
    @media (max-width: 768px) {
        .stForm {
            padding: 10px;
        }
    }
</style>
