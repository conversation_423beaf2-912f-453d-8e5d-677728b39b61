# نظام إدارة ديون الطلاب

هذا النظام يساعد في إدارة ديون الطلاب وتتبع المدفوعات وعرض التقارير.

## متطلبات النظام

- Python 3.10 أو أحدث
- قاعدة بيانات PostgreSQL

## خطوات التثبيت

### 1. تثبيت بايثون
تأكد من تثبيت بايثون 3.10 أو أحدث على الخادم الخاص بك.

### 2. إعداد قاعدة بيانات PostgreSQL
1. قم بإنشاء قاعدة بيانات جديدة باسم `student_debts` أو أي اسم تختاره.
2. قم بتسجيل بيانات الاتصال بقاعدة البيانات (اسم المستخدم، كلمة المرور، اسم قاعدة البيانات، المضيف، المنفذ).

### 3. إعداد متغيرات البيئة
أنشئ ملف `.env` في مجلد المشروع وأضف إليه بيانات الاتصال بقاعدة البيانات:

```
DATABASE_URL=postgresql://username:password@localhost:5432/student_debts
PGUSER=username
PGPASSWORD=password
PGDATABASE=student_debts
PGHOST=localhost
PGPORT=5432
```

استبدل `username` و `password` والمعلومات الأخرى بالمعلومات الخاصة بك.

### 4. تثبيت المكتبات المطلوبة
في مجلد المشروع، قم بتنفيذ الأمر التالي لتثبيت المكتبات المطلوبة:

```bash
pip install streamlit pandas plotly sqlalchemy psycopg2-binary openpyxl
```

### 5. إنشاء جداول قاعدة البيانات
قم بتشغيل الأمر التالي لإنشاء جداول قاعدة البيانات:

```bash
python -c "from database import Base, engine; Base.metadata.create_all(engine)"
```

## تشغيل التطبيق

```bash
streamlit run app.py --server.port 8501 --server.address 0.0.0.0
```

بعد ذلك، يمكنك الوصول إلى التطبيق عبر المتصفح على العنوان: `http://your_server_ip:8501`

## إعداد التطبيق للتشغيل كخدمة

للتأكد من استمرار تشغيل التطبيق حتى بعد إغلاق جلسة SSH، يمكنك إعداده كخدمة باستخدام systemd:

1. أنشئ ملف خدمة جديد:

```bash
sudo nano /etc/systemd/system/studentdebt.service
```

2. أضف المحتوى التالي (قم بتعديل المسار حسب موقع تثبيت المشروع):

```
[Unit]
Description=Student Debt Management System
After=network.target

[Service]
User=your_username
WorkingDirectory=/path/to/project
ExecStart=/usr/bin/python3 -m streamlit run app.py --server.port 8501 --server.address 0.0.0.0
Restart=always
StandardOutput=syslog
StandardError=syslog
SyslogIdentifier=studentdebt
Environment="PATH=/path/to/python/venv/bin:/usr/local/bin:/usr/bin:/bin"
Environment="DATABASE_URL=postgresql://username:password@localhost:5432/student_debts"

[Install]
WantedBy=multi-user.target
```

3. قم بتفعيل وتشغيل الخدمة:

```bash
sudo systemctl daemon-reload
sudo systemctl enable studentdebt
sudo systemctl start studentdebt
```

## النسخ الاحتياطي

لعمل نسخة احتياطية من قاعدة البيانات، استخدم الأمر التالي:

```bash
pg_dump -U username -d student_debts > backup_$(date +%Y%m%d).sql
```

## استعادة النسخة الاحتياطية

لاستعادة النسخة الاحتياطية، استخدم الأمر التالي:

```bash
psql -U username -d student_debts < backup_file.sql
```

## ملاحظات إضافية

- تأكد من ضبط إعدادات الجدار الناري للسماح بالوصول إلى المنفذ 8501 (أو المنفذ الذي اخترته)
- يمكنك إعداد خادم وكيل عكسي مثل Nginx لتوفير اتصال HTTPS آمن