import pandas as pd
import numpy as np
import os
from io import BytesIO

class DebtManager:
    """
    فئة لإدارة بيانات ديون الطلاب بما في ذلك الإضافة والتحديث والتخزين.
    """
    
    def __init__(self):
        # تهيئة DataFrame فارغ لتخزين سجلات الديون
        self.df = pd.DataFrame(columns=['Student Name', 'Debt Amount'])
    
    def add_debt(self, student_name, debt_amount):
        """
        Add a new student debt record
        
        Args:
            student_name (str): Name of the student
            debt_amount (float): Amount of debt
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Check if student already exists
            if student_name in self.df['Student Name'].values:
                return False
            
            # Add new row to DataFrame
            new_row = pd.DataFrame({
                'Student Name': [student_name],
                'Debt Amount': [debt_amount]
            })
            
            self.df = pd.concat([self.df, new_row], ignore_index=True)
            return True
        except Exception as e:
            print(f"Error adding debt: {e}")
            return False
    
    def update_debt(self, student_name, debt_amount):
        """
        Update an existing student debt record
        
        Args:
            student_name (str): Name of the student
            debt_amount (float): New amount of debt
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Check if student exists
            if student_name not in self.df['Student Name'].values:
                return False
            
            # Update debt amount
            self.df.loc[self.df['Student Name'] == student_name, 'Debt Amount'] = debt_amount
            return True
        except Exception as e:
            print(f"Error updating debt: {e}")
            return False
    
    def delete_debt(self, student_name):
        """
        Delete a student debt record
        
        Args:
            student_name (str): Name of the student
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Check if student exists
            if student_name not in self.df['Student Name'].values:
                return False
            
            # Delete record
            self.df = self.df[self.df['Student Name'] != student_name]
            return True
        except Exception as e:
            print(f"Error deleting debt: {e}")
            return False
    
    def get_debt(self, student_name):
        """
        Get debt amount for a specific student
        
        Args:
            student_name (str): Name of the student
            
        Returns:
            float or None: Debt amount if student found, None otherwise
        """
        try:
            if student_name not in self.df['Student Name'].values:
                return None
            
            return self.df.loc[self.df['Student Name'] == student_name, 'Debt Amount'].values[0]
        except Exception as e:
            print(f"Error getting debt: {e}")
            return None
    
    def get_all_students(self):
        """
        Get list of all student names
        
        Returns:
            list: List of student names
        """
        return self.df['Student Name'].tolist()
    
    def get_total_debt(self):
        """
        Calculate total debt across all students
        
        Returns:
            float: Total debt amount
        """
        return self.df['Debt Amount'].sum()
    
    def get_dataframe(self):
        """
        Get the entire DataFrame
        
        Returns:
            DataFrame: Debt records DataFrame
        """
        return self.df.copy()
    
    def is_empty(self):
        """
        Check if there are any debt records
        
        Returns:
            bool: True if no records, False otherwise
        """
        return len(self.df) == 0
    
    def import_from_excel(self, file):
        """
        Import debt records from an Excel file
        
        Args:
            file: File object or path to Excel file
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Read Excel file
            imported_df = pd.read_excel(file)
            
            # Validate columns
            required_columns = ['Student Name', 'Debt Amount']
            if not all(col in imported_df.columns for col in required_columns):
                return False
            
            # Keep only required columns
            imported_df = imported_df[required_columns]
            
            # Validate data types
            if not pd.api.types.is_numeric_dtype(imported_df['Debt Amount']):
                # Try to convert to numeric, coercing errors to NaN
                imported_df['Debt Amount'] = pd.to_numeric(imported_df['Debt Amount'], errors='coerce')
                # Drop rows with NaN debt amounts
                imported_df = imported_df.dropna(subset=['Debt Amount'])
            
            # Replace existing data
            self.df = imported_df
            return True
        except Exception as e:
            print(f"Error importing from Excel: {e}")
            return False
    
    def export_to_excel(self, file_path):
        """
        Export debt records to an Excel file
        
        Args:
            file_path (str): Path to save Excel file
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Export to Excel
            self.df.to_excel(file_path, index=False)
            return True
        except Exception as e:
            print(f"Error exporting to Excel: {e}")
            return False
