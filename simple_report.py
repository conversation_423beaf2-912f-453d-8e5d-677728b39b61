"""
وحدة بسيطة جداً لإنشاء تقارير PDF مع التركيز على المتانة والموثوقية
تستخدم هذه الوحدة مكتبة FPDF مع الحد الأدنى من المميزات 
وتركز على الاستقرار بدلاً من المظهر المتطور
"""
import io
import base64
import pandas as pd
from datetime import datetime
from fpdf import FPDF


class SimpleReport(FPDF):
    """فئة بسيطة للتقارير مع الحد الأدنى من المميزات للتركيز على الموثوقية"""
    
    def __init__(self, orientation='P', unit='mm', format='A4'):
        super().__init__(orientation, unit, format)
        self.add_font('DejaVu', '', 'DejaVuSansCondensed.ttf', uni=True)
        self.add_font('DejaVu', 'B', 'DejaVuSansCondensed.ttf', uni=True)
        self.add_font('DejaVu', 'I', 'DejaVuSansCondensed.ttf', uni=True)
        self.add_page()
    
    def add_report_header(self, title):
        """إضافة ترويسة التقرير مع العنوان والتاريخ"""
        try:
            # العنوان
            self.set_font('DejaVu', 'B', 16)
            self.cell(0, 10, title, 0, 1, 'C')
            
            # التاريخ
            self.set_font('DejaVu', '', 10)
            current_date = datetime.now().strftime('%Y-%m-%d %H:%M')
            self.cell(0, 6, f'تاريخ التقرير: {current_date}', 0, 1, 'L')
            self.ln(5)
        except Exception as e:
            print(f"خطأ في إضافة ترويسة التقرير: {str(e)}")
    
    def add_section_header(self, title):
        """إضافة عنوان قسم"""
        try:
            self.set_font('DejaVu', 'B', 12)
            self.cell(0, 8, title, 0, 1, 'R')
            self.ln(2)
        except Exception as e:
            print(f"خطأ في إضافة عنوان قسم: {str(e)}")
    
    def add_simple_table(self, headers, data, col_widths=None, header_height=8, row_height=7, border=1):
        """
        إضافة جدول بسيط بطريقة موثوقة
        
        Args:
            headers (list): قائمة عناوين الأعمدة
            data (list): قائمة الصفوف، حيث كل صف هو قائمة من القيم
            col_widths (list, optional): قائمة بعرض الأعمدة، إذا كانت None سيتم تقسيم العرض بالتساوي
            header_height (int): ارتفاع صف العناوين
            row_height (int): ارتفاع صفوف البيانات
            border (int): نمط الحدود (0 = بدون، 1 = حدود كاملة)
        """
        try:
            if not headers or not data:
                self.set_font('DejaVu', '', 10)
                self.cell(0, 8, "لا توجد بيانات متاحة للعرض", 0, 1, 'C')
                return
            
            num_cols = len(headers)
            
            # تحديد عرض الأعمدة
            if col_widths is None:
                page_width = self.w - 2 * self.l_margin
                col_widths = [page_width / num_cols] * num_cols
            
            # رؤوس الجدول
            self.set_font('DejaVu', 'B', 10)
            self.set_fill_color(240, 240, 240)  # لون رمادي فاتح للرؤوس
            
            for i, header in enumerate(headers):
                try:
                    align = 'C'
                    self.cell(col_widths[i], header_height, str(header), border, 0, align, True)
                except Exception as e:
                    # في حال وجود خطأ في عنوان معين، نضع نصاً بديلاً
                    self.cell(col_widths[i], header_height, "خطأ", border, 0, 'C', True)
            self.ln()
            
            # بيانات الجدول
            self.set_font('DejaVu', '', 10)
            
            # لكل صف من البيانات
            for row in data:
                # التأكد من أن الصف يحتوي على عدد صحيح من الأعمدة
                if len(row) != num_cols:
                    # تكملة الصف بقيم فارغة إذا كان ناقصاً
                    row = row + [""] * (num_cols - len(row)) if len(row) < num_cols else row[:num_cols]
                
                # طباعة خلايا الصف
                for i, cell_data in enumerate(row):
                    try:
                        # محاولة تحويل البيانات إلى نص
                        cell_text = str(cell_data) if cell_data is not None else ""
                        
                        # تحديد المحاذاة حسب موقع العمود
                        if i == num_cols - 1:  # العمود الأخير على اليمين (غالباً أسماء)
                            align = 'R'
                        elif i == 0:  # العمود الأول على اليسار
                            align = 'L'
                        else:  # الأعمدة الوسطى (غالباً أرقام)
                            align = 'C'
                        
                        self.cell(col_widths[i], row_height, cell_text, border, 0, align)
                    except Exception as e:
                        # في حال وجود خطأ في خلية معينة
                        self.cell(col_widths[i], row_height, "خطأ", border, 0, 'C')
                self.ln()
        except Exception as e:
            print(f"خطأ في إنشاء الجدول: {str(e)}")
            self.set_font('DejaVu', '', 10)
            self.multi_cell(0, 8, f"حدث خطأ أثناء إنشاء الجدول: {str(e)}", 0, 'R')
    
    def add_key_value_pair(self, key, value, width_key=70):
        """إضافة زوج مفتاح-قيمة بتنسيق متسق"""
        try:
            self.set_font('DejaVu', '', 10)
            self.cell(width_key, 8, str(key), 0, 0, 'R')
            self.cell(0, 8, str(value), 0, 1, 'R')
        except Exception as e:
            print(f"خطأ في إضافة زوج مفتاح-قيمة: {str(e)}")
    
    def add_footer_note(self, note):
        """إضافة ملاحظة في تذييل الصفحة"""
        try:
            self.ln(10)
            self.set_font('DejaVu', 'I', 8)
            self.cell(0, 6, note, 0, 1, 'C')
        except Exception as e:
            print(f"خطأ في إضافة ملاحظة التذييل: {str(e)}")


def format_currency(amount):
    """تنسيق المبلغ بأسلوب العملة"""
    try:
        # التأكد من أن المبلغ رقم
        if amount is None:
            return "0"
        
        # محاولة تحويل المبلغ إلى رقم إذا كان نصاً
        if isinstance(amount, str):
            try:
                amount = float(amount)
            except (ValueError, TypeError):
                return "0"
        
        # تقريب المبلغ إلى رقمين عشريين وتنسيقه
        return f"{round(float(amount), 2):,.2f}"
    except:
        return "0"


def create_all_students_report(df):
    """
    إنشاء تقرير لجميع الطلاب
    
    Args:
        df: إطار بيانات يحتوي على بيانات جميع الطلاب
        
    Returns:
        bytes: محتوى ملف PDF، ولن يكون None أبدًا
    """
    try:
        # التأكد من أن df ليس None
        if df is None:
            df = pd.DataFrame()  # إنشاء إطار بيانات فارغ
            
        # التعامل مع جدول بيانات فارغ
        if df.empty:
            # إنشاء تقرير فارغ
            pdf = SimpleReport()
            pdf.add_report_header("تقرير جميع الطلاب")
            pdf.add_section_header("لا توجد بيانات")
            pdf.set_font('DejaVu', '', 10)
            pdf.cell(0, 10, "لا توجد بيانات طلاب متاحة", 0, 1, 'C')
            pdf.add_footer_note("المبالغ موضحة بالفلس الكويتي.")
            try:
                return pdf.output(dest='S').encode('latin-1')
            except Exception as e:
                print(f"خطأ عند محاولة إخراج PDF فارغ: {str(e)}")
                # إنشاء PDF بسيط جداً في حالة الفشل
                pdf = FPDF()
                pdf.add_page()
                return pdf.output(dest='S').encode('latin-1')
        
        # حساب المجاميع
        try:
            # محاولة حساب المجاميع بأمان
            total_debt = df['Debt Amount'].fillna(0).astype(float).sum()
            total_paid = df['Paid Amount'].fillna(0).astype(float).sum()
            total_remaining = df['Remaining Amount'].fillna(0).astype(float).sum()
            student_count = len(df)
        except Exception as e:
            print(f"خطأ في حساب المجاميع: {str(e)}")
            total_debt = 0
            total_paid = 0
            total_remaining = 0
            student_count = 0
        
        # إنشاء التقرير
        pdf = SimpleReport()
        
        # إضافة الترويسة والملخص
        pdf.add_report_header("تقرير جميع الطلاب")
        pdf.add_section_header("ملخص")
        
        # إضافة معلومات الملخص
        pdf.add_key_value_pair("إجمالي عدد الطلاب:", str(student_count))
        pdf.add_key_value_pair("إجمالي مبلغ الدين:", format_currency(total_debt))
        pdf.add_key_value_pair("إجمالي المبلغ المسدد:", format_currency(total_paid))
        pdf.add_key_value_pair("إجمالي المبلغ المتبقي:", format_currency(total_remaining))
        
        pdf.ln(10)
        pdf.add_section_header("قائمة الطلاب")
        
        # تحضير بيانات الجدول
        headers = ["نسبة السداد", "المبلغ المتبقي", "المبلغ المسدد", "مبلغ الدين", "اسم الطالب"]
        
        # عرض العمود
        col_widths = [30, 35, 35, 35, 50]
        
        # تحضير بيانات الصفوف
        data = []
        for _, row in df.iterrows():
            try:
                # استخراج البيانات مع التعامل الآمن
                student_name = str(row.get('Student Name', 'غير معروف'))
                
                try:
                    debt_amount = float(row.get('Debt Amount', 0) or 0)
                except:
                    debt_amount = 0
                
                try:
                    paid_amount = float(row.get('Paid Amount', 0) or 0)
                except:
                    paid_amount = 0
                
                try:
                    remaining_amount = float(row.get('Remaining Amount', 0) or 0)
                except:
                    remaining_amount = max(0, debt_amount - paid_amount)
                
                # حساب نسبة السداد
                if debt_amount > 0:
                    payment_percentage = (paid_amount / debt_amount) * 100
                else:
                    payment_percentage = 0
                
                # إضافة الصف إلى البيانات
                data.append([
                    f"{round(payment_percentage, 1)}%",
                    format_currency(remaining_amount),
                    format_currency(paid_amount),
                    format_currency(debt_amount),
                    student_name
                ])
            except Exception as e:
                print(f"خطأ في معالجة صف من البيانات: {str(e)}")
        
        # إضافة الجدول للتقرير
        pdf.add_simple_table(headers, data, col_widths)
        
        # إضافة ملاحظة التذييل
        pdf.add_footer_note("المبالغ موضحة بالفلس الكويتي.")
        
        # إرجاع التقرير كبايتات
        return pdf.output(dest='S').encode('latin-1')
    
    except Exception as e:
        print(f"خطأ في إنشاء تقرير جميع الطلاب: {str(e)}")
        
        # إنشاء تقرير بسيط بمعلومات الخطأ
        try:
            pdf = SimpleReport()
            pdf.add_report_header("خطأ في إنشاء التقرير")
            pdf.set_font('DejaVu', '', 12)
            pdf.multi_cell(0, 10, f"حدث خطأ أثناء إنشاء التقرير: {str(e)}", 0, 'R')
            return pdf.output(dest='S').encode('latin-1')
        except Exception as e1:
            print(f"خطأ عند محاولة إنشاء تقرير بسيط للخطأ: {str(e1)}")
            # إنشاء PDF بسيط جداً في حالة الفشل
            try:
                pdf = FPDF()
                pdf.add_page()
                return pdf.output(dest='S').encode('latin-1')
            except Exception as e2:
                print(f"خطأ عند محاولة إنشاء PDF بسيط: {str(e2)}")
                # الحالة الأسوأ - إرجاع بيانات PDF فارغة لكنها صالحة
                return b'%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/Resources <<\n>>\n/MediaBox [0 0 595 842]\n/Contents 4 0 R\n>>\nendobj\n4 0 obj\n<< /Length 0 >>\nstream\nendstream\nendobj\nxref\n0 5\n0000000000 65535 f \n0000000009 00000 n \n0000000058 00000 n \n0000000115 00000 n \n0000000210 00000 n \ntrailer\n<<\n/Size 5\n/Root 1 0 R\n>>\nstartxref\n260\n%%EOF'


def create_student_report(student_name, debt_data, transactions_df):
    """
    إنشاء تقرير تفصيلي لطالب معين
    
    Args:
        student_name: اسم الطالب
        debt_data: قاموس يحتوي على بيانات الدين
        transactions_df: إطار بيانات يحتوي على سجل المعاملات
        
    Returns:
        bytes: محتوى ملف PDF أو None في حالة الخطأ
    """
    try:
        # التحقق من البيانات
        if not student_name:
            student_name = "طالب غير معروف"
        
        if not debt_data:
            debt_data = {}
        
        # استخراج بيانات الدين
        try:
            debt_amount = float(debt_data.get('debt_amount', 0) or 0)
        except:
            debt_amount = 0
        
        try:
            paid_amount = float(debt_data.get('paid_amount', 0) or 0)
        except:
            paid_amount = 0
        
        # حساب القيم المشتقة
        remaining_amount = max(0, debt_amount - paid_amount)
        
        if debt_amount > 0:
            payment_percentage = (paid_amount / debt_amount) * 100
        else:
            payment_percentage = 0
        
        # تحديد حالة السداد
        if payment_percentage >= 100:
            payment_status = "مسدد بالكامل"
        elif payment_percentage > 0:
            payment_status = "مسدد جزئياً"
        else:
            payment_status = "غير مسدد"
        
        # إنشاء التقرير
        pdf = SimpleReport()
        
        # إضافة الترويسة
        pdf.add_report_header(f"تقرير الطالب: {student_name}")
        
        # إضافة بيانات الدين
        pdf.add_section_header("معلومات الدين")
        pdf.add_key_value_pair("مبلغ الدين:", format_currency(debt_amount))
        pdf.add_key_value_pair("المبلغ المسدد:", format_currency(paid_amount))
        pdf.add_key_value_pair("المبلغ المتبقي:", format_currency(remaining_amount))
        pdf.add_key_value_pair("حالة السداد:", payment_status)
        pdf.add_key_value_pair("نسبة السداد:", f"{round(payment_percentage, 1)}%")
        
        # إضافة سجل المعاملات
        pdf.ln(10)
        pdf.add_section_header("سجل المعاملات")
        
        if transactions_df is not None and not transactions_df.empty:
            # تحضير بيانات الجدول
            headers = ["ملاحظات", "المبلغ", "نوع المعاملة", "التاريخ"]
            col_widths = [60, 30, 45, 45]
            
            # تحضير بيانات الصفوف
            data = []
            for _, row in transactions_df.iterrows():
                try:
                    # معالجة نوع المعاملة
                    transaction_type = 'دفع' if row.get('Transaction Type') == 'payment' else 'دين'
                    
                    # معالجة التاريخ
                    if isinstance(row.get('Transaction Date'), str):
                        date_str = row.get('Transaction Date', '')
                    else:
                        date_obj = row.get('Transaction Date')
                        if date_obj:
                            date_str = date_obj.strftime('%Y-%m-%d')
                        else:
                            date_str = ''
                    
                    # معالجة المبلغ والملاحظات
                    amount = row.get('Amount', 0)
                    notes = str(row.get('Notes', '')) if row.get('Notes') else ''
                    
                    # إضافة الصف
                    data.append([
                        notes[:30],  # اقتصار الملاحظات على 30 حرف
                        format_currency(amount),
                        transaction_type,
                        date_str
                    ])
                except Exception as e:
                    print(f"خطأ في معالجة صف من المعاملات: {str(e)}")
            
            # إضافة جدول المعاملات
            pdf.add_simple_table(headers, data, col_widths)
        else:
            pdf.set_font('DejaVu', '', 10)
            pdf.cell(0, 10, "لا توجد معاملات مسجلة", 0, 1, 'C')
        
        # إضافة ملاحظة التذييل
        pdf.add_footer_note("المبالغ موضحة بالفلس الكويتي.")
        
        # إرجاع التقرير كبايتات
        return pdf.output(dest='S').encode('latin-1')
    
    except Exception as e:
        print(f"خطأ في إنشاء تقرير الطالب: {str(e)}")
        
        # إنشاء تقرير بسيط بمعلومات الخطأ
        try:
            pdf = SimpleReport()
            pdf.add_report_header("خطأ في إنشاء التقرير")
            pdf.set_font('DejaVu', '', 12)
            pdf.multi_cell(0, 10, f"حدث خطأ أثناء إنشاء تقرير الطالب: {str(e)}", 0, 'R')
            return pdf.output(dest='S').encode('latin-1')
        except Exception as e1:
            print(f"خطأ عند محاولة إنشاء تقرير بسيط للخطأ: {str(e1)}")
            # إنشاء PDF بسيط جداً في حالة الفشل
            try:
                pdf = FPDF()
                pdf.add_page()
                return pdf.output(dest='S').encode('latin-1')
            except Exception as e2:
                print(f"خطأ عند محاولة إنشاء PDF بسيط: {str(e2)}")
                # الحالة الأسوأ - إرجاع بيانات PDF فارغة لكنها صالحة
                return b'%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/Resources <<\n>>\n/MediaBox [0 0 595 842]\n/Contents 4 0 R\n>>\nendobj\n4 0 obj\n<< /Length 0 >>\nstream\nendstream\nendobj\nxref\n0 5\n0000000000 65535 f \n0000000009 00000 n \n0000000058 00000 n \n0000000115 00000 n \n0000000210 00000 n \ntrailer\n<<\n/Size 5\n/Root 1 0 R\n>>\nstartxref\n260\n%%EOF'


def create_payment_status_report(fully_paid_df, partially_paid_df, unpaid_df):
    """
    إنشاء تقرير حالة السداد للطلاب
    
    Args:
        fully_paid_df: إطار بيانات الطلاب المسددين بالكامل
        partially_paid_df: إطار بيانات الطلاب المسددين جزئياً
        unpaid_df: إطار بيانات الطلاب غير المسددين
        
    Returns:
        bytes: محتوى ملف PDF أو None في حالة الخطأ
    """
    try:
        # التحقق من البيانات
        if fully_paid_df is None:
            fully_paid_df = pd.DataFrame()
        if partially_paid_df is None:
            partially_paid_df = pd.DataFrame()
        if unpaid_df is None:
            unpaid_df = pd.DataFrame()
        
        # حساب إجماليات
        fully_paid_count = len(fully_paid_df)
        partially_paid_count = len(partially_paid_df)
        unpaid_count = len(unpaid_df)
        total_students = fully_paid_count + partially_paid_count + unpaid_count
        
        # إنشاء التقرير
        pdf = SimpleReport()
        
        # إضافة الترويسة والملخص
        pdf.add_report_header("تقرير حالة سداد الطلاب")
        pdf.add_section_header("ملخص")
        
        # إضافة ملخص الحالات
        pdf.add_key_value_pair("الطلاب المسددين بالكامل:", str(fully_paid_count))
        pdf.add_key_value_pair("الطلاب المسددين جزئياً:", str(partially_paid_count))
        pdf.add_key_value_pair("الطلاب غير المسددين:", str(unpaid_count))
        pdf.add_key_value_pair("إجمالي عدد الطلاب:", str(total_students))
        
        # التحقق من وجود بيانات
        if total_students == 0:
            pdf.ln(10)
            pdf.set_font('DejaVu', '', 12)
            pdf.cell(0, 10, "لا توجد بيانات للطلاب", 0, 1, 'C')
            pdf.add_footer_note("المبالغ موضحة بالفلس الكويتي.")
            return pdf.output(dest='S').encode('latin-1')
        
        # إضافة قسم الطلاب المسددين بالكامل
        if not fully_paid_df.empty:
            pdf.ln(10)
            pdf.add_section_header(f"الطلاب المسددين بالكامل ({str(fully_paid_count)})")
            
            # تحضير بيانات الجدول
            headers = ["المبلغ المتبقي", "المبلغ المسدد", "مبلغ الدين", "اسم الطالب"]
            col_widths = [45, 45, 45, 45]
            
            # تحضير بيانات الصفوف
            data = []
            for _, row in fully_paid_df.iterrows():
                try:
                    # استخراج البيانات
                    student_name = str(row.get('Student Name', 'غير معروف'))
                    debt_amount = row.get('Debt Amount', 0)
                    paid_amount = row.get('Paid Amount', 0)
                    remaining_amount = row.get('Remaining Amount', 0)
                    
                    # إضافة الصف
                    data.append([
                        format_currency(remaining_amount),
                        format_currency(paid_amount),
                        format_currency(debt_amount),
                        student_name
                    ])
                except Exception as e:
                    print(f"خطأ في معالجة صف من بيانات المسددين بالكامل: {str(e)}")
            
            # إضافة الجدول
            pdf.add_simple_table(headers, data, col_widths)
        
        # إضافة قسم الطلاب المسددين جزئياً
        if not partially_paid_df.empty:
            pdf.ln(10)
            pdf.add_section_header(f"الطلاب المسددين جزئياً ({str(partially_paid_count)})")
            
            # تحضير بيانات الجدول
            headers = ["نسبة السداد", "المبلغ المتبقي", "المبلغ المسدد", "مبلغ الدين", "اسم الطالب"]
            col_widths = [25, 35, 35, 35, 50]
            
            # تحضير بيانات الصفوف
            data = []
            for _, row in partially_paid_df.iterrows():
                try:
                    # استخراج البيانات
                    student_name = str(row.get('Student Name', 'غير معروف'))
                    debt_amount = row.get('Debt Amount', 0)
                    paid_amount = row.get('Paid Amount', 0)
                    remaining_amount = row.get('Remaining Amount', 0)
                    
                    # حساب نسبة السداد
                    if 'Payment Percentage' in row:
                        if isinstance(row['Payment Percentage'], (int, float)):
                            payment_percentage = row['Payment Percentage']
                        elif isinstance(row['Payment Percentage'], str):
                            # إزالة علامة % إذا كانت موجودة
                            clean_percent = row['Payment Percentage'].replace('%', '')
                            try:
                                payment_percentage = float(clean_percent)
                            except:
                                payment_percentage = 0
                    else:
                        # حساب النسبة
                        if isinstance(debt_amount, (int, float)) and debt_amount > 0:
                            payment_percentage = (float(paid_amount) / float(debt_amount)) * 100
                        else:
                            payment_percentage = 0
                    
                    # إضافة الصف
                    data.append([
                        f"{round(payment_percentage, 1)}%",
                        format_currency(remaining_amount),
                        format_currency(paid_amount),
                        format_currency(debt_amount),
                        student_name
                    ])
                except Exception as e:
                    print(f"خطأ في معالجة صف من بيانات المسددين جزئياً: {str(e)}")
            
            # إضافة الجدول
            pdf.add_simple_table(headers, data, col_widths)
        
        # إضافة قسم الطلاب غير المسددين
        if not unpaid_df.empty:
            pdf.ln(10)
            pdf.add_section_header(f"الطلاب غير المسددين ({str(unpaid_count)})")
            
            # تحضير بيانات الجدول
            headers = ["المبلغ المتبقي", "المبلغ المسدد", "مبلغ الدين", "اسم الطالب"]
            col_widths = [45, 45, 45, 45]
            
            # تحضير بيانات الصفوف
            data = []
            for _, row in unpaid_df.iterrows():
                try:
                    # استخراج البيانات
                    student_name = str(row.get('Student Name', 'غير معروف'))
                    debt_amount = row.get('Debt Amount', 0)
                    paid_amount = row.get('Paid Amount', 0)
                    remaining_amount = row.get('Remaining Amount', 0)
                    
                    # إضافة الصف
                    data.append([
                        format_currency(remaining_amount),
                        format_currency(paid_amount),
                        format_currency(debt_amount),
                        student_name
                    ])
                except Exception as e:
                    print(f"خطأ في معالجة صف من بيانات غير المسددين: {str(e)}")
            
            # إضافة الجدول
            pdf.add_simple_table(headers, data, col_widths)
        
        # إضافة ملاحظة التذييل
        pdf.add_footer_note("المبالغ موضحة بالفلس الكويتي.")
        
        # إرجاع التقرير كبايتات
        return pdf.output(dest='S').encode('latin-1')
    
    except Exception as e:
        print(f"خطأ في إنشاء تقرير حالة السداد: {str(e)}")
        
        # إنشاء تقرير بسيط بمعلومات الخطأ
        try:
            pdf = SimpleReport()
            pdf.add_report_header("خطأ في إنشاء التقرير")
            pdf.set_font('DejaVu', '', 12)
            pdf.multi_cell(0, 10, f"حدث خطأ أثناء إنشاء تقرير حالة السداد: {str(e)}", 0, 'R')
            return pdf.output(dest='S').encode('latin-1')
        except Exception as e1:
            print(f"خطأ عند محاولة إنشاء تقرير بسيط للخطأ: {str(e1)}")
            # إنشاء PDF بسيط جداً في حالة الفشل
            try:
                pdf = FPDF()
                pdf.add_page()
                return pdf.output(dest='S').encode('latin-1')
            except Exception as e2:
                print(f"خطأ عند محاولة إنشاء PDF بسيط: {str(e2)}")
                # الحالة الأسوأ - إرجاع بيانات PDF فارغة لكنها صالحة
                return b'%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/Resources <<\n>>\n/MediaBox [0 0 595 842]\n/Contents 4 0 R\n>>\nendobj\n4 0 obj\n<< /Length 0 >>\nstream\nendstream\nendobj\nxref\n0 5\n0000000000 65535 f \n0000000009 00000 n \n0000000058 00000 n \n0000000115 00000 n \n0000000210 00000 n \ntrailer\n<<\n/Size 5\n/Root 1 0 R\n>>\nstartxref\n260\n%%EOF'


def create_download_link(pdf_bytes, filename):
    """
    إنشاء رابط لتنزيل ملف PDF
    
    Args:
        pdf_bytes: محتوى ملف PDF كبايتات
        filename: اسم الملف عند التنزيل
        
    Returns:
        str: كود HTML لرابط التنزيل أو رسالة خطأ
    """
    try:
        if pdf_bytes is None:
            return "فشل إنشاء الملف"
        
        # ترميز البيانات بصيغة base64
        b64 = base64.b64encode(pdf_bytes).decode()
        href = f'<a href="data:application/pdf;base64,{b64}" download="{filename}" class="download-link">تنزيل الملف</a>'
        return href
    except Exception as e:
        print(f"خطأ في إنشاء رابط التنزيل: {str(e)}")
        return "فشل إنشاء رابط التنزيل"