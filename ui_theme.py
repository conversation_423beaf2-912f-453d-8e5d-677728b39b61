"""
Módulo para gestionar los temas de la aplicación.
Proporciona temas claro y oscuro, y funciones para aplicarlos.
"""

# Tema claro
LIGHT_THEME = {
    "primary": "#1E88E5",       # Azul principal
    "secondary": "#26A69A",     # Verde-azulado secundario
    "background": "#FFFFFF",    # Fondo blanco
    "surface": "#F5F7FA",       # Superficie de tarjetas
    "text": "#212121",          # Texto principal
    "text_secondary": "#757575", # Texto secundario
    "border": "#E0E0E0",        # Bordes
    "success": "#4CAF50",       # Verde para éxito
    "warning": "#FFC107",       # Amarillo para advertencias
    "error": "#F44336",         # Rojo para errores
    "info": "#2196F3",          # Azul para información
}

# Tema oscuro
DARK_THEME = {
    "primary": "#90CAF9",       # Azul principal más claro
    "secondary": "#80CBC4",     # Verde-azulado secundario más claro
    "background": "#121212",    # Fondo oscuro
    "surface": "#1E1E1E",       # Superficie de tarjetas
    "text": "#FFFFFF",          # Texto principal
    "text_secondary": "#B0B0B0", # Texto secundario
    "border": "#333333",        # Bordes
    "success": "#81C784",       # Verde para éxito
    "warning": "#FFD54F",       # Amarillo para advertencias
    "error": "#E57373",         # Rojo para errores
    "info": "#64B5F6",          # Azul para información
}

def get_theme_css(theme):
    """Genera CSS basado en el tema seleccionado"""
    return f"""
    :root {{
        --primary: {theme["primary"]};
        --secondary: {theme["secondary"]};
        --background: {theme["background"]};
        --surface: {theme["surface"]};
        --text: {theme["text"]};
        --text-secondary: {theme["text_secondary"]};
        --border: {theme["border"]};
        --success: {theme["success"]};
        --warning: {theme["warning"]};
        --error: {theme["error"]};
        --info: {theme["info"]};
    }}
    
    /* Aplicar colores de tema a elementos Streamlit */
    .stApp {{
        background-color: var(--background);
        color: var(--text);
    }}
    
    .sidebar .sidebar-content {{
        background-color: var(--surface);
    }}
    
    h1, h2, h3, h4, h5, h6 {{
        color: var(--primary);
    }}
    
    .stTextInput > div > div > input {{
        background-color: var(--surface);
        color: var(--text);
    }}
    
    .stSelectbox > div > div > div {{
        background-color: var(--surface);
        color: var(--text);
    }}
    
    .stDataFrame {{
        background-color: var(--surface);
    }}
    """

def apply_theme(theme_name="فاتح"):
    """
    Aplica el tema seleccionado (claro u oscuro)
    
    Args:
        theme_name (str): Nombre del tema ('فاتح' o 'داكن')
    
    Returns:
        str: CSS para aplicar el tema
    """
    theme = LIGHT_THEME if theme_name == 'فاتح' else DARK_THEME
    
    theme_css = get_theme_css(theme)
    
    # CSS base
    base_css = """
    /* Estilos generales */
    .main .block-container {
        padding-top: 2rem;
        padding-bottom: 2rem;
    }
    
    /* Estilos para tarjetas */
    .dashboard-card {
        background-color: var(--surface);
        border-radius: 10px;
        padding: 1.5rem;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        margin-bottom: 1rem;
    }
    
    /* Estilos para tablas */
    .dataframe {
        width: 100%;
        border-collapse: collapse;
    }
    .dataframe th {
        background-color: var(--surface);
        padding: 10px;
        text-align: center;
        font-weight: bold;
        border: 1px solid var(--border);
    }
    .dataframe td {
        padding: 8px;
        border: 1px solid var(--border);
        text-align: center;
    }
    .dataframe tr:hover {
        background-color: var(--surface);
    }
    
    /* Estilos para botones */
    .stButton>button {
        font-weight: bold;
        border-radius: 6px;
        padding: 0.5rem 1rem;
        transition: all 0.2s;
    }
    .stButton>button:hover {
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        transform: translateY(-2px);
    }
    
    /* Estilos para formularios */
    .stForm {
        background-color: var(--surface);
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 20px;
    }
    
    /* Estilos para métricas */
    .stMetric {
        background-color: var(--surface);
        border-radius: 8px;
        padding: 10px;
        box-shadow: 0 1px 2px rgba(0,0,0,0.05);
    }
    
    /* Estilos para dispositivos móviles */
    @media (max-width: 768px) {
        .stForm {
            padding: 10px;
        }
        
        .dashboard-card {
            padding: 1rem;
        }
        
        .dataframe th, .dataframe td {
            padding: 6px 4px;
            font-size: 12px;
        }
        
        .stButton>button {
            padding: 0.3rem 0.6rem;
            font-size: 12px;
        }
    }
    """
    
    return f"{theme_css}{base_css}"
