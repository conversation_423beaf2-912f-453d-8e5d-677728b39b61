"""
وحدة لإنشاء وتصدير تقارير PDF
"""
import os
import base64
import tempfile
from datetime import datetime
import io
import pandas as pd
from fpdf import FPDF
import streamlit as st

from utils import format_currency

class PDF(FPDF):
    """فئة معدلة من FPDF لدعم الترويسة والتذييل"""
    
    def header(self):
        """ترويسة الصفحة"""
        # اللوجو
        # self.image('logo.png', 10, 8, 33)
        # العنوان
        self.set_font('Arial', 'B', 15)
        self.cell(0, 10, 'نظام إدارة ديون الطلاب', 0, 1, 'C')
        self.ln(10)
    
    def footer(self):
        """تذييل الصفحة"""
        self.set_y(-15)
        self.set_font('Arial', 'I', 8)
        self.cell(0, 10, f'الصفحة {self.page_no()}', 0, 0, 'C')


def convert_df_to_html(df, title="جدول البيانات"):
    """
    تحويل DataFrame إلى HTML
    """
    # تنسيق التاريخ والأرقام العشرية
    styled_df = df.copy()
    
    # إنشاء HTML
    html = f"""
    <html dir="rtl">
    <head>
        <meta charset="UTF-8">
        <style>
            body {{
                font-family: Arial, Tahoma, sans-serif;
                text-align: right;
                direction: rtl;
            }}
            h2 {{
                color: #4b6cb7;
                text-align: center;
            }}
            table {{
                width: 100%;
                border-collapse: collapse;
                margin: 20px 0;
            }}
            th, td {{
                border: 1px solid #ddd;
                padding: 12px 8px;
                text-align: right;
            }}
            th {{
                background-color: #4b6cb7;
                color: white;
                padding-top: 12px;
                padding-bottom: 12px;
            }}
            tr:nth-child(even) {{
                background-color: #f2f2f2;
            }}
        </style>
    </head>
    <body>
        <h2>{title}</h2>
        {styled_df.to_html(index=False)}
    </body>
    </html>
    """
    return html


def generate_summary_html(df):
    """
    إنشاء ملخص إحصائي في HTML
    """
    total_debt = df['Debt Amount'].sum()
    total_paid = df['Paid Amount'].sum()
    total_remaining = df['Remaining Amount'].sum()
    avg_debt = df['Debt Amount'].mean()
    payment_ratio = (total_paid / total_debt) * 100 if total_debt > 0 else 0
    students_count = len(df)
    
    html = f"""
    <html dir="rtl">
    <head>
        <meta charset="UTF-8">
        <style>
            body {{
                font-family: Arial, Tahoma, sans-serif;
                text-align: right;
                direction: rtl;
            }}
            h2, h3 {{
                color: #4b6cb7;
                text-align: center;
            }}
            .summary-container {{
                display: flex;
                flex-wrap: wrap;
                justify-content: space-around;
                margin: 20px 0;
            }}
            .summary-item {{
                border: 1px solid #ddd;
                border-radius: 8px;
                padding: 15px;
                margin: 10px;
                text-align: center;
                width: 25%;
                box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            }}
            .summary-title {{
                font-weight: bold;
                color: #555;
            }}
            .summary-value {{
                font-size: 20px;
                margin-top: 5px;
                color: #4b6cb7;
            }}
            .timestamp {{
                text-align: center;
                font-size: 12px;
                color: #777;
                margin-top: 30px;
            }}
        </style>
    </head>
    <body>
        <h2>ملخص إحصائيات الديون</h2>
        
        <div class="summary-container">
            <div class="summary-item">
                <div class="summary-title">إجمالي الديون</div>
                <div class="summary-value">{format_currency(total_debt)}</div>
            </div>
            <div class="summary-item">
                <div class="summary-title">إجمالي المسدد</div>
                <div class="summary-value">{format_currency(total_paid)}</div>
            </div>
            <div class="summary-item">
                <div class="summary-title">إجمالي المتبقي</div>
                <div class="summary-value">{format_currency(total_remaining)}</div>
            </div>
        </div>
        
        <div class="summary-container">
            <div class="summary-item">
                <div class="summary-title">متوسط الدين</div>
                <div class="summary-value">{format_currency(avg_debt)}</div>
            </div>
            <div class="summary-item">
                <div class="summary-title">نسبة السداد</div>
                <div class="summary-value">{payment_ratio:.1f}%</div>
            </div>
            <div class="summary-item">
                <div class="summary-title">عدد الطلاب</div>
                <div class="summary-value">{students_count}</div>
            </div>
        </div>
        
        <div class="timestamp">
            تاريخ التقرير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
        </div>
    </body>
    </html>
    """
    return html


def plot_to_image():
    """تحويل الرسم البياني الحالي إلى صورة"""
    img_buf = io.BytesIO()
    plt.savefig(img_buf, format='png', bbox_inches='tight', dpi=300)
    img_buf.seek(0)
    return img_buf


def generate_student_report_html(student_name, debt_data, transactions_df):
    """
    إنشاء تقرير تفصيلي لطالب معين كملف HTML
    
    Args:
        student_name (str): اسم الطالب
        debt_data (dict): بيانات دين الطالب
        transactions_df (DataFrame): سجل معاملات الطالب
        
    Returns:
        str: محتوى HTML للتقرير
    """
    # تنسيق المبالغ من دينار إلى فلس
    debt_amount = debt_data.get('debt_amount', 0) * 1000
    paid_amount = debt_data.get('paid_amount', 0) * 1000
    remaining_amount = debt_amount - paid_amount
    
    # تحويل المبالغ في سجل المعاملات من دينار إلى فلس
    transactions_html = ""
    if not transactions_df.empty:
        trans_df = transactions_df.copy()
        trans_df['Amount'] = trans_df['Amount'] * 1000
        trans_df['Formatted Amount'] = trans_df['Amount'].apply(lambda x: format_currency(x))
        trans_df['Transaction Date'] = trans_df['Transaction Date'].apply(
            lambda x: x.strftime("%Y-%m-%d %H:%M:%S") if hasattr(x, 'strftime') else x
        )
        
        # إعداد DataFrame للعرض
        display_df = trans_df[['Transaction Type', 'Formatted Amount', 'Transaction Date', 'Notes']].rename(
            columns={'Formatted Amount': 'Amount'}
        )
        transactions_html = display_df.to_html(index=False)
    
    html = f"""
    <html dir="rtl">
    <head>
        <meta charset="UTF-8">
        <style>
            body {{
                font-family: Arial, Tahoma, sans-serif;
                text-align: right;
                direction: rtl;
                padding: 20px;
            }}
            h1, h2, h3 {{
                color: #4b6cb7;
                text-align: center;
            }}
            .student-info {{
                display: flex;
                flex-wrap: wrap;
                justify-content: space-around;
                margin: 20px 0;
                border: 1px solid #ddd;
                border-radius: 8px;
                padding: 15px;
                background-color: #f9f9f9;
            }}
            .info-item {{
                text-align: center;
                padding: 10px;
                width: 30%;
            }}
            .info-title {{
                font-weight: bold;
                color: #555;
            }}
            .info-value {{
                font-size: 18px;
                margin-top: 5px;
                color: #4b6cb7;
            }}
            table {{
                width: 100%;
                border-collapse: collapse;
                margin: 20px 0;
            }}
            th, td {{
                border: 1px solid #ddd;
                padding: 12px 8px;
                text-align: right;
            }}
            th {{
                background-color: #4b6cb7;
                color: white;
                padding-top: 12px;
                padding-bottom: 12px;
            }}
            tr:nth-child(even) {{
                background-color: #f2f2f2;
            }}
            .timestamp {{
                text-align: center;
                font-size: 12px;
                color: #777;
                margin-top: 30px;
            }}
            .no-data {{
                text-align: center;
                color: #777;
                font-style: italic;
                margin: 20px 0;
            }}
        </style>
    </head>
    <body>
        <h1>تقرير الديون للطالب</h1>
        <h2>{student_name}</h2>
        
        <div class="student-info">
            <div class="info-item">
                <div class="info-title">إجمالي الدين</div>
                <div class="info-value">{format_currency(debt_amount)}</div>
            </div>
            <div class="info-item">
                <div class="info-title">المبلغ المسدد</div>
                <div class="info-value">{format_currency(paid_amount)}</div>
            </div>
            <div class="info-item">
                <div class="info-title">المبلغ المتبقي</div>
                <div class="info-value">{format_currency(remaining_amount)}</div>
            </div>
        </div>
        
        <h3>سجل المعاملات</h3>
        {transactions_html if transactions_html else '<div class="no-data">لا توجد معاملات مسجلة لهذا الطالب.</div>'}
        
        <div class="timestamp">
            تاريخ التقرير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
        </div>
    </body>
    </html>
    """
    return html


def export_all_students_pdf(df):
    """
    تصدير بيانات جميع الطلاب إلى ملف PDF
    
    Args:
        df (DataFrame): بيانات الطلاب
        
    Returns:
        BytesIO: كائن ذاكرة يحتوي على ملف PDF
    """
    # تحويل المبالغ من دينار إلى فلس
    export_df = df.copy()
    for col in ['Debt Amount', 'Paid Amount', 'Remaining Amount']:
        export_df[col] = export_df[col] * 1000
        export_df[f'Formatted {col}'] = export_df[col].apply(lambda x: format_currency(x))
    
    # إنشاء نسخة للعرض مع الأعمدة المنسقة
    display_df = export_df[['Student Name', 'Formatted Debt Amount', 'Formatted Paid Amount', 'Formatted Remaining Amount']].rename(
        columns={
            'Student Name': 'اسم الطالب',
            'Formatted Debt Amount': 'إجمالي الدين (فلس)',
            'Formatted Paid Amount': 'المبلغ المسدد (فلس)',
            'Formatted Remaining Amount': 'المبلغ المتبقي (فلس)'
        }
    )
    
    # إعداد الإحصائيات للتقرير
    total_debt = df['Debt Amount'].sum() * 1000  # تحويل إلى فلس
    total_paid = df['Paid Amount'].sum() * 1000  # تحويل إلى فلس
    total_remaining = total_debt - total_paid
    avg_debt = df['Debt Amount'].mean() * 1000  # تحويل إلى فلس
    payment_ratio = (total_paid / total_debt) * 100 if total_debt > 0 else 0
    students_count = len(df)
    
    # إنشاء PDF
    pdf = PDF('L', 'mm', 'A4')  # Landscape
    pdf.add_page()
    
    # إضافة العنوان
    pdf.set_font('Arial', 'B', 16)
    pdf.cell(0, 10, 'تقرير ديون الطلاب', 0, 1, 'C')
    pdf.ln(5)
    
    # إضافة تاريخ التقرير
    pdf.set_font('Arial', 'I', 10)
    pdf.cell(0, 10, f'تاريخ التقرير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}', 0, 1, 'C')
    pdf.ln(5)
    
    # قسم الإحصائيات
    pdf.set_font('Arial', 'B', 14)
    pdf.cell(0, 10, 'ملخص إحصائيات الديون', 0, 1, 'C')
    pdf.ln(5)
    
    # عرض الإحصائيات في جدول
    pdf.set_font('Arial', '', 12)
    col_width = 60
    
    # الصف الأول
    pdf.cell(col_width, 10, 'إجمالي الديون', 1, 0, 'C')
    pdf.cell(col_width, 10, 'إجمالي المسدد', 1, 0, 'C')
    pdf.cell(col_width, 10, 'إجمالي المتبقي', 1, 1, 'C')
    
    pdf.cell(col_width, 10, format_currency(total_debt), 1, 0, 'C')
    pdf.cell(col_width, 10, format_currency(total_paid), 1, 0, 'C')
    pdf.cell(col_width, 10, format_currency(total_remaining), 1, 1, 'C')
    
    pdf.ln(5)
    
    # الصف الثاني
    pdf.cell(col_width, 10, 'متوسط الدين', 1, 0, 'C')
    pdf.cell(col_width, 10, 'نسبة السداد', 1, 0, 'C')
    pdf.cell(col_width, 10, 'عدد الطلاب', 1, 1, 'C')
    
    pdf.cell(col_width, 10, format_currency(avg_debt), 1, 0, 'C')
    pdf.cell(col_width, 10, f'{payment_ratio:.1f}%', 1, 0, 'C')
    pdf.cell(col_width, 10, str(students_count), 1, 1, 'C')
    
    pdf.ln(10)
    
    # جدول بيانات الطلاب
    pdf.add_page()
    pdf.set_font('Arial', 'B', 14)
    pdf.cell(0, 10, 'بيانات ديون الطلاب', 0, 1, 'C')
    pdf.ln(5)
    
    # عناوين الأعمدة
    columns = ['اسم الطالب', 'إجمالي الدين (فلس)', 'المبلغ المسدد (فلس)', 'المبلغ المتبقي (فلس)']
    col_width = pdf.w / len(columns) - 10
    
    pdf.set_font('Arial', 'B', 12)
    for col in columns:
        pdf.cell(col_width, 10, col, 1, 0, 'C')
    pdf.ln()
    
    # إضافة البيانات
    pdf.set_font('Arial', '', 10)
    for _, row in display_df.iterrows():
        for col in display_df.columns:
            pdf.cell(col_width, 10, str(row[col]), 1, 0, 'C')
        pdf.ln()
    
    # إخراج الملف
    pdf_bytes = io.BytesIO()
    pdf.output(pdf_bytes)
    pdf_bytes.seek(0)
    
    return pdf_bytes


def export_student_report_pdf(student_name, debt_data, transactions_df):
    """
    تصدير تقرير تفصيلي لطالب معين إلى ملف PDF
    
    Args:
        student_name (str): اسم الطالب
        debt_data (dict): بيانات دين الطالب
        transactions_df (DataFrame): سجل معاملات الطالب
        
    Returns:
        BytesIO: كائن ذاكرة يحتوي على ملف PDF
    """
    # تنسيق المبالغ من دينار إلى فلس
    debt_amount = debt_data.get('debt_amount', 0) * 1000
    paid_amount = debt_data.get('paid_amount', 0) * 1000
    remaining_amount = debt_amount - paid_amount
    
    # إنشاء PDF باستخدام FPDF
    pdf = PDF('P', 'mm', 'A4')  # Portrait
    pdf.add_page()
    
    # إضافة العنوان
    pdf.set_font('Arial', 'B', 16)
    pdf.cell(0, 10, 'تقرير الديون للطالب', 0, 1, 'C')
    pdf.set_font('Arial', 'B', 14)
    pdf.cell(0, 10, student_name, 0, 1, 'C')
    pdf.ln(5)
    
    # إضافة تاريخ التقرير
    pdf.set_font('Arial', 'I', 10)
    pdf.cell(0, 10, f'تاريخ التقرير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}', 0, 1, 'C')
    pdf.ln(10)
    
    # معلومات الدين للطالب
    pdf.set_font('Arial', 'B', 14)
    pdf.cell(0, 10, 'ملخص الدين', 0, 1, 'C')
    pdf.ln(5)
    
    # جدول ملخص الدين
    pdf.set_font('Arial', 'B', 12)
    col_width = 60
    
    pdf.cell(col_width, 10, 'إجمالي الدين', 1, 0, 'C')
    pdf.cell(col_width, 10, 'المبلغ المسدد', 1, 0, 'C')
    pdf.cell(col_width, 10, 'المبلغ المتبقي', 1, 1, 'C')
    
    pdf.set_font('Arial', '', 12)
    pdf.cell(col_width, 10, format_currency(debt_amount), 1, 0, 'C')
    pdf.cell(col_width, 10, format_currency(paid_amount), 1, 0, 'C')
    pdf.cell(col_width, 10, format_currency(remaining_amount), 1, 1, 'C')
    
    pdf.ln(20)
    
    # سجل المعاملات
    if not transactions_df.empty:
        pdf.set_font('Arial', 'B', 14)
        pdf.cell(0, 10, 'سجل المعاملات', 0, 1, 'C')
        pdf.ln(5)
        
        # إعداد DataFrame للعرض
        trans_df = transactions_df.copy()
        trans_df['Amount'] = trans_df['Amount'] * 1000
        trans_df['Formatted Amount'] = trans_df['Amount'].apply(lambda x: format_currency(x))
        
        # عناوين الأعمدة
        columns = ['نوع المعاملة', 'المبلغ', 'تاريخ المعاملة', 'ملاحظات']
        col_width = pdf.w / len(columns) - 10
        
        pdf.set_font('Arial', 'B', 12)
        for col in columns:
            pdf.cell(col_width, 10, col, 1, 0, 'C')
        pdf.ln()
        
        # إضافة البيانات
        pdf.set_font('Arial', '', 10)
        for _, row in trans_df.iterrows():
            # تحويل التاريخ إلى نص
            if hasattr(row['Transaction Date'], 'strftime'):
                date_str = row['Transaction Date'].strftime("%Y-%m-%d %H:%M:%S")
            else:
                date_str = str(row['Transaction Date'])
                
            # تجهيز البيانات للعرض
            transaction_type = 'دين' if row['Transaction Type'] == 'debt' else 'تسديد'
            amount = str(row['Formatted Amount'])
            notes = str(row['Notes']) if pd.notna(row['Notes']) else ''
            
            # إضافة الصف
            pdf.cell(col_width, 10, transaction_type, 1, 0, 'C')
            pdf.cell(col_width, 10, amount, 1, 0, 'C')
            pdf.cell(col_width, 10, date_str, 1, 0, 'C')
            pdf.cell(col_width, 10, notes, 1, 1, 'C')
    else:
        pdf.set_font('Arial', 'I', 12)
        pdf.cell(0, 10, 'لا توجد معاملات مسجلة لهذا الطالب.', 0, 1, 'C')
    
    # إخراج الملف
    pdf_bytes = io.BytesIO()
    pdf.output(pdf_bytes)
    pdf_bytes.seek(0)
    
    return pdf_bytes


def create_download_link(pdf_bytes, filename):
    """إنشاء رابط تنزيل للملف"""
    b64 = base64.b64encode(pdf_bytes.read()).decode()
    return f'<a href="data:application/octet-stream;base64,{b64}" download="{filename}">انقر هنا للتنزيل</a>'