# محتويات مشروع نظام إدارة ديون الطلاب

هذا المستند يشرح محتويات هذا المشروع وكيفية تعديله في المستقبل.

## الملفات الرئيسية

### `app.py`
- الملف الرئيسي للتطبيق
- يحتوي على واجهة المستخدم المبنية بواسطة Streamlit
- يدير عرض الصفحات المختلفة (إضافة/تحديث الديون، عرض الديون، التقارير، الاستيراد/التصدير)

### `database.py`
- تعريف قاعدة البيانات وإدارة الاتصال بها
- يحتوي على تعريفات الجداول (نموذج الدين، نموذج المعاملات)
- يتضمن فئة `DatabaseManager` التي تدير عمليات قاعدة البيانات

### `debt_manager.py`
- إدارة منطق الأعمال لديون الطلاب
- يوفر واجهة بسيطة للعمليات المتعلقة بالديون
- يستخدم `DatabaseManager` في الخلفية

### `utils.py`
- وظائف مساعدة متنوعة
- يتضمن وظائف التحقق من صحة المدخلات
- يحتوي على وظائف تنسيق العملة

### `visualization.py`
- إنشاء الرسوم البيانية والتصور
- يستخدم مكتبة Plotly لإنشاء الرسوم البيانية التفاعلية

## ملفات التثبيت والتوثيق

### `README.md`
- توثيق مفصل للمشروع
- تعليمات التثبيت والإعداد
- المتطلبات والخصائص

### `INSTALL.txt`
- تعليمات التثبيت السريعة
- دليل مرجعي سريع للمستخدمين

### `setup_database.py`
- سكريبت لإعداد قاعدة البيانات
- يمكن استخدامه لإنشاء وتهيئة قاعدة البيانات

### `install.sh`
- سكريبت تثبيت تفاعلي
- يقوم بتثبيت المتطلبات وإعداد قاعدة البيانات

### `اعداد_ملف_مضغوط.md`
- تعليمات إنشاء ملف مضغوط للتوزيع
- إرشادات للتوزيع على خوادم أخرى

## كيفية تعديل النظام

### تعديل واجهة المستخدم
لتعديل واجهة المستخدم أو إضافة صفحات جديدة، قم بتعديل الملف `app.py`:

1. لإضافة صفحة جديدة:
   - أضف خيارًا جديدًا في قائمة التنقل
   ```python
   page = st.sidebar.radio("اذهب إلى", ["إضافة/تحديث الديون", "عرض جميع الديون", "التقارير", "استيراد/تصدير", "الصفحة الجديدة"])
   ```
   
   - أضف شرطًا جديدًا للصفحة:
   ```python
   elif page == "الصفحة الجديدة":
       st.header("عنوان الصفحة الجديدة")
       # محتوى الصفحة
   ```

2. لتعديل تنسيق العملة:
   - قم بتعديل الدالة `format_currency` في ملف `utils.py`

### تعديل قاعدة البيانات
لإضافة حقول جديدة إلى قاعدة البيانات:

1. قم بتعديل نماذج قاعدة البيانات في ملف `database.py`:
   ```python
   class Debt(Base):
       __tablename__ = "debts"
       
       # الحقول الحالية
       ...
       
       # إضافة حقل جديد
       new_field = Column(String, nullable=True)
   ```

2. قم بإنشاء جداول قاعدة البيانات مرة أخرى:
   ```bash
   python setup_database.py --setup-db
   ```

### إضافة تقارير جديدة
لإضافة تقارير وتصورات جديدة:

1. أضف دالة جديدة في ملف `visualization.py`:
   ```python
   def create_new_chart(df):
       """
       إنشاء رسم بياني جديد
       
       Args:
           df (DataFrame): إطار بيانات البيانات
           
       Returns:
           Figure: كائن الرسم البياني
       """
       # إنشاء الرسم البياني
       ...
       return fig
   ```

2. استخدم الدالة الجديدة في `app.py`:
   ```python
   new_chart = create_new_chart(df)
   st.plotly_chart(new_chart, use_container_width=True)
   ```

### إضافة وظائف جديدة
لإضافة وظائف جديدة إلى إدارة الديون:

1. أضف الدوال اللازمة في `database.py` للتعامل مع قاعدة البيانات:
   ```python
   def new_database_function(self, param1, param2):
       """
       وظيفة قاعدة بيانات جديدة
       """
       # تنفيذ الوظيفة
       ...
   ```

2. قم بتحديث الواجهة في `debt_manager.py` عند الحاجة:
   ```python
   def new_business_function(self, param1, param2):
       """
       وظيفة منطق أعمال جديدة
       """
       # استدعاء وظيفة قاعدة البيانات
       ...
   ```

3. استخدم الوظيفة الجديدة في `app.py`:
   ```python
   if st.button("زر للوظيفة الجديدة"):
       result = st.session_state.debt_manager.new_business_function(param1, param2)
       if result:
           st.success("تم تنفيذ الوظيفة بنجاح!")
   ```

## نصائح للتطوير المستقبلي

1. **الحفاظ على النسخ الاحتياطية**: تأكد من عمل نسخ احتياطية منتظمة لقاعدة البيانات
2. **التعليقات**: احرص على إضافة تعليقات كافية عند تعديل الكود
3. **الاختبار**: اختبر التغييرات في بيئة تطوير قبل نشرها في الإنتاج
4. **البناء التدريجي**: قم بإجراء تغييرات صغيرة وتدريجية واختبرها بدلاً من التغييرات الكبيرة دفعة واحدة
5. **التوثيق**: احرص على تحديث وثائق المشروع مع كل تعديل أو إضافة وظيفة جديدة

## مخطط قاعدة البيانات

### جدول `debts` (الديون)
- `id`: معرف فريد (مفتاح أساسي)
- `student_name`: اسم الطالب
- `debt_amount`: قيمة الدين
- `paid_amount`: المبلغ المسدد

### جدول `transactions` (المعاملات)
- `id`: معرف فريد (مفتاح أساسي)
- `student_id`: معرف الطالب (مفتاح أجنبي يشير إلى جدول debts)
- `transaction_type`: نوع المعاملة ("debt" للدين، "payment" للتسديد)
- `amount`: مبلغ المعاملة
- `transaction_date`: تاريخ المعاملة
- `notes`: ملاحظات إضافية