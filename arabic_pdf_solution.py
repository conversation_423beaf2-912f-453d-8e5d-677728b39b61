"""
وحدة محسنة لإنشاء وتصدير تقارير PDF مع دعم كامل للنصوص العربية
يستخدم هذا الإصدار تقنية مختلفة للتعامل مع النصوص العربية
"""
import os
import base64
import tempfile
from datetime import datetime
import io
import pandas as pd
from fpdf import FPDF
import streamlit as st

from utils import format_currency

class ArabicPDF(FPDF):
    """فئة FPDF معدلة مع دعم أفضل للنصوص العربية عن طريق تحويل الاتجاه"""
    
    def __init__(self):
        # تهيئة بالإعدادات الافتراضية
        super().__init__(orientation='P', unit='mm', format='A4')
        # ضبط صفحة الترميز لدعم النصوص العربية
        self.add_font('DejaVu', '', os.path.join(os.path.dirname(__file__), 'DejaVuSansCondensed.ttf'), uni=True)
        # تعيين اتجاه من اليمين إلى اليسار للصفحة
        self.set_right_margin(15)
        self.set_left_margin(15)
        self.set_auto_page_break(True, 15)
        
    def header(self):
        """ترويسة الصفحة"""
        # شعار أو عنوان الصفحة
        self.set_font('DejaVu', '', 15)
        self.cell(0, 10, 'نظام إدارة ديون الطلاب', 0, 1, 'C')
        self.ln(5)
    
    def footer(self):
        """تذييل الصفحة"""
        # الانتقال إلى موضع التذييل
        self.set_y(-15)
        self.set_font('DejaVu', '', 8)
        # رقم الصفحة
        self.cell(0, 10, f'الصفحة {str(self.page_no())}', 0, 0, 'C')
        # التاريخ
        date_str = datetime.now().strftime('%Y-%m-%d')
        self.cell(0, 10, date_str, 0, 0, 'L')
    
    def rtl_cell(self, w, h, txt, border=0, ln=0, align='', fill=False, direction='R'):
        """خلية معدلة للنصوص العربية من اليمين إلى اليسار"""
        # استخدام محاذاة من اليمين للنصوص العربية
        if align == '':
            align = 'R'  # الافتراضي للنصوص العربية هو المحاذاة لليمين
        
        # استخدام خط DejaVu للنصوص العربية
        if self.font_family != 'DejaVu':
            self.set_font('DejaVu', '', self.font_size_pt)
        
        self.cell(w, h, txt, border, ln, align, fill)


def create_pdf_buffer(content_function):
    """
    دالة مساعدة لإنشاء كائن ذاكرة PDF بطريقة آمنة باستخدام ملفات مؤقتة
    
    Args:
        content_function: دالة تضيف محتوى إلى كائن PDF
        
    Returns:
        BytesIO: كائن ذاكرة يحتوي على ملف PDF
    """
    try:
        # إنشاء ملف مؤقت
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as tmp:
            tmp_path = tmp.name
        
        # إنشاء وحفظ PDF
        pdf = content_function()
        pdf.output(tmp_path)
        
        # قراءة الملف المؤقت في كائن ذاكرة
        with open(tmp_path, 'rb') as f:
            buffer = io.BytesIO(f.read())
        
        # حذف الملف المؤقت
        os.unlink(tmp_path)
        
        # إعادة تعيين مؤشر كائن الذاكرة إلى البداية
        buffer.seek(0)
        
        return buffer
        
    except Exception as e:
        # إنشاء PDF للخطأ
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as tmp:
            tmp_path = tmp.name
        
        error_pdf = ArabicPDF()
        error_pdf.add_page()
        error_pdf.set_font('DejaVu', '', 16)
        error_pdf.cell(0, 10, "خطأ في إنشاء تقرير PDF", 0, 1, 'C')
        error_pdf.ln(10)
        error_pdf.set_font('DejaVu', '', 12)
        error_pdf.cell(0, 10, f"حدث خطأ أثناء إنشاء تقرير PDF: {str(e)}", 0, 1, 'R')
        error_pdf.cell(0, 10, "يرجى عرض البيانات في التطبيق بدلاً من ذلك.", 0, 1, 'R')
        
        error_pdf.output(tmp_path)
        
        # قراءة الملف المؤقت في كائن ذاكرة
        with open(tmp_path, 'rb') as f:
            buffer = io.BytesIO(f.read())
        
        # حذف الملف المؤقت
        os.unlink(tmp_path)
        
        # إعادة تعيين مؤشر كائن الذاكرة إلى البداية
        buffer.seek(0)
        
        return buffer


def export_all_students_pdf(df):
    """
    تصدير بيانات جميع الطلاب إلى ملف PDF
    
    Args:
        df (DataFrame): بيانات الطلاب
        
    Returns:
        BytesIO: كائن ذاكرة يحتوي على ملف PDF
    """
    def create_content():
        # إنشاء ملف PDF جديد
        pdf = ArabicPDF()
        pdf.add_page()
        
        # عنوان التقرير
        pdf.set_font('DejaVu', '', 16)
        pdf.rtl_cell(0, 10, 'تقرير ديون جميع الطلاب', 0, 1, 'C')
        pdf.ln(10)
        
        # البيانات الإحصائية
        pdf.set_font('DejaVu', '', 12)
        pdf.rtl_cell(0, 10, 'ملخص الإحصائيات', 0, 1, 'R')
        
        # القيم الإحصائية
        pdf.set_font('DejaVu', '', 10)
        total_students = len(df)
        total_debt = df['Debt Amount'].sum()
        total_paid = df['Paid Amount'].sum()
        total_remaining = df['Remaining Amount'].sum()
        payment_ratio = (total_paid / total_debt * 100) if total_debt > 0 else 0
        
        pdf.rtl_cell(70, 8, 'إجمالي عدد الطلاب:', 0, 0, 'R')
        pdf.rtl_cell(0, 8, str(total_students), 0, 1, 'R')
        
        pdf.rtl_cell(70, 8, 'إجمالي الدين:', 0, 0, 'R')
        pdf.rtl_cell(0, 8, f'{format_currency(total_debt)}', 0, 1, 'R')
        
        pdf.rtl_cell(70, 8, 'إجمالي المبلغ المسدد:', 0, 0, 'R')
        pdf.rtl_cell(0, 8, f'{format_currency(total_paid)}', 0, 1, 'R')
        
        pdf.rtl_cell(70, 8, 'إجمالي المبلغ المتبقي:', 0, 0, 'R')
        pdf.rtl_cell(0, 8, f'{format_currency(total_remaining)}', 0, 1, 'R')
        
        pdf.rtl_cell(70, 8, 'نسبة السداد:', 0, 0, 'R')
        pdf.rtl_cell(0, 8, f"{payment_ratio:.1f}%", 0, 1, 'R')
        
        pdf.ln(10)
        
        # جدول بيانات الطلاب
        pdf.set_font('DejaVu', '', 12)
        pdf.rtl_cell(0, 10, 'سجلات الطلاب', 0, 1, 'R')
        
        # رؤوس الجدول
        pdf.set_font('DejaVu', '', 10)
        cell_width = 45
        pdf.rtl_cell(cell_width, 8, 'المبلغ المتبقي', 1, 0, 'C')
        pdf.rtl_cell(cell_width, 8, 'المبلغ المسدد', 1, 0, 'C')
        pdf.rtl_cell(cell_width, 8, 'مبلغ الدين', 1, 0, 'C')
        pdf.rtl_cell(cell_width, 8, 'اسم الطالب', 1, 1, 'C')
        
        # بيانات الجدول
        pdf.set_font('DejaVu', '', 10)
        for _, row in df.iterrows():
            remaining_amount = row['Remaining Amount']
            paid_amount = row['Paid Amount']
            debt_amount = row['Debt Amount']
            student_name = row['Student Name']
            
            pdf.rtl_cell(cell_width, 8, format_currency(remaining_amount), 1, 0, 'C')
            pdf.rtl_cell(cell_width, 8, format_currency(paid_amount), 1, 0, 'C')
            pdf.rtl_cell(cell_width, 8, format_currency(debt_amount), 1, 0, 'C')
            pdf.rtl_cell(cell_width, 8, student_name, 1, 1, 'R')
        
        return pdf
    
    return create_pdf_buffer(create_content)


def export_student_report_pdf(student_name, debt_data, transactions_df):
    """
    تصدير تقرير تفصيلي لطالب معين إلى ملف PDF
    
    Args:
        student_name (str): اسم الطالب
        debt_data (dict): بيانات دين الطالب
        transactions_df (DataFrame): سجل معاملات الطالب
        
    Returns:
        BytesIO: كائن ذاكرة يحتوي على ملف PDF
    """
    def create_content():
        # إنشاء ملف PDF جديد
        pdf = ArabicPDF()
        pdf.add_page()
        
        # عنوان التقرير
        pdf.set_font('DejaVu', '', 16)
        pdf.rtl_cell(0, 10, f'تقرير الطالب: {student_name}', 0, 1, 'C')
        pdf.ln(10)
        
        # ملخص بيانات الطالب
        pdf.set_font('DejaVu', '', 12)
        pdf.rtl_cell(0, 10, 'معلومات الطالب', 0, 1, 'R')
        
        # بيانات الطالب
        pdf.set_font('DejaVu', '', 10)
        pdf.rtl_cell(70, 8, 'اسم الطالب:', 0, 0, 'R')
        pdf.rtl_cell(0, 8, student_name, 0, 1, 'R')
        
        # القيم المالية - مع التأكد من التحويل الصحيح إلى أرقام
        debt_amount = float(debt_data["debt_amount"]) if not isinstance(debt_data["debt_amount"], (int, float)) else debt_data["debt_amount"]
        paid_amount = float(debt_data["paid_amount"]) if not isinstance(debt_data["paid_amount"], (int, float)) else debt_data["paid_amount"]
        
        pdf.rtl_cell(70, 8, 'مبلغ الدين:', 0, 0, 'R')
        pdf.rtl_cell(0, 8, f'{format_currency(debt_amount)}', 0, 1, 'R')
        
        pdf.rtl_cell(70, 8, 'المبلغ المسدد:', 0, 0, 'R')
        pdf.rtl_cell(0, 8, f'{format_currency(paid_amount)}', 0, 1, 'R')
        
        # حساب المبلغ المتبقي مع مراعاة التنسيق
        remaining_amount = debt_data.get("remaining_amount")
        if remaining_amount is None:
            remaining_amount = debt_amount - paid_amount
        else:
            remaining_amount = float(remaining_amount) if not isinstance(remaining_amount, (int, float)) else remaining_amount
            
        pdf.rtl_cell(70, 8, 'المبلغ المتبقي:', 0, 0, 'R')
        pdf.rtl_cell(0, 8, f'{format_currency(remaining_amount)}', 0, 1, 'R')
        
        # حساب نسبة السداد
        payment_percentage = debt_data.get("payment_percentage")
        if payment_percentage is None:
            payment_percentage = (paid_amount / debt_amount * 100) if debt_amount > 0 else 0
        else:
            payment_percentage = float(payment_percentage) if not isinstance(payment_percentage, (int, float)) else payment_percentage
        pdf.rtl_cell(70, 8, 'نسبة السداد:', 0, 0, 'R')
        pdf.rtl_cell(0, 8, f'{payment_percentage:.1f}%', 0, 1, 'R')
        
        pdf.ln(10)
        
        # سجل المعاملات
        pdf.set_font('DejaVu', '', 12)
        pdf.rtl_cell(0, 10, 'سجل المعاملات', 0, 1, 'R')
        
        if transactions_df.empty:
            pdf.set_font('DejaVu', '', 10)
            pdf.rtl_cell(0, 8, 'لا توجد معاملات مسجلة لهذا الطالب.', 0, 1, 'R')
        else:
            # رؤوس جدول المعاملات
            pdf.set_font('DejaVu', '', 10)
            pdf.rtl_cell(70, 8, 'ملاحظات', 1, 0, 'C')
            pdf.rtl_cell(40, 8, 'المبلغ', 1, 0, 'C')
            pdf.rtl_cell(30, 8, 'النوع', 1, 0, 'C')
            pdf.rtl_cell(40, 8, 'التاريخ', 1, 1, 'C')
            
            # بيانات جدول المعاملات
            pdf.set_font('DejaVu', '', 10)
            
            # التحقق من أسماء الأعمدة المتوقعة
            date_col = 'Transaction Date' if 'Transaction Date' in transactions_df.columns else 'Date'
            type_col = 'Transaction Type' if 'Transaction Type' in transactions_df.columns else 'Type'
            notes_col = 'Notes' if 'Notes' in transactions_df.columns else None
            
            for _, row in transactions_df.iterrows():
                transaction_type = "تسديد" if row[type_col].lower() == 'payment' else "دين"
                
                # تنسيق التاريخ
                date_val = row[date_col]
                date_str = date_val.strftime('%Y-%m-%d') if hasattr(date_val, 'strftime') else str(date_val)
                
                # معالجة الملاحظات
                notes = row[notes_col] if notes_col and isinstance(row[notes_col], str) else ""
                notes = notes if notes else ""
                
                pdf.rtl_cell(70, 8, (notes[:25] + "...") if len(notes) > 25 else notes, 1, 0, 'R')
                pdf.rtl_cell(40, 8, format_currency(row['Amount']), 1, 0, 'C')
                pdf.rtl_cell(30, 8, transaction_type, 1, 0, 'C')
                pdf.rtl_cell(40, 8, date_str, 1, 1, 'C')
        
        return pdf
    
    return create_pdf_buffer(create_content)


def export_payment_status_report_pdf(fully_paid_df, partially_paid_df, unpaid_df):
    """
    تصدير تقرير حالة السداد للطلاب إلى ملف PDF.
    يعرض قوائم للطلاب المسددين بالكامل، المسددين جزئياً، وغير المسددين
    
    Args:
        fully_paid_df (DataFrame): إطار بيانات الطلاب المسددين بالكامل
        partially_paid_df (DataFrame): إطار بيانات الطلاب المسددين جزئياً
        unpaid_df (DataFrame): إطار بيانات الطلاب غير المسددين
        
    Returns:
        BytesIO: كائن ذاكرة يحتوي على ملف PDF
    """
    def create_content():
        # إنشاء ملف PDF جديد
        pdf = ArabicPDF()
        pdf.add_page()
        
        # عنوان التقرير
        pdf.set_font('DejaVu', '', 16)
        pdf.rtl_cell(0, 10, 'تقرير حالة سداد الديون', 0, 1, 'C')
        pdf.ln(10)
        
        # الملخص
        pdf.set_font('DejaVu', '', 12)
        pdf.rtl_cell(0, 10, 'ملخص', 0, 1, 'R')
        
        pdf.set_font('DejaVu', '', 10)
        # تأكد من التعامل مع الأرقام على هيئة نصوص
        pdf.rtl_cell(70, 8, 'الطلاب المسددين بالكامل:', 0, 0, 'R')
        pdf.rtl_cell(0, 8, str(len(fully_paid_df)), 0, 1, 'R')
        
        pdf.rtl_cell(70, 8, 'الطلاب المسددين جزئياً:', 0, 0, 'R')
        pdf.rtl_cell(0, 8, str(len(partially_paid_df)), 0, 1, 'R')
        
        pdf.rtl_cell(70, 8, 'الطلاب غير المسددين:', 0, 0, 'R')
        pdf.rtl_cell(0, 8, str(len(unpaid_df)), 0, 1, 'R')
        
        total_students = len(fully_paid_df) + len(partially_paid_df) + len(unpaid_df)
        pdf.rtl_cell(70, 8, 'إجمالي عدد الطلاب:', 0, 0, 'R')
        pdf.rtl_cell(0, 8, str(total_students), 0, 1, 'R')
        
        # الطلاب المسددين بالكامل
        if not fully_paid_df.empty:
            pdf.ln(10)
            pdf.set_font('DejaVu', '', 12)
            fully_paid_count = len(fully_paid_df)
            title = 'الطلاب المسددين بالكامل (' + str(fully_paid_count) + ')'
            pdf.rtl_cell(0, 10, title, 0, 1, 'R')
            
            # رؤوس الجدول
            pdf.set_font('DejaVu', '', 10)
            cell_width = 45
            pdf.rtl_cell(cell_width, 8, 'المبلغ المتبقي', 1, 0, 'C')
            pdf.rtl_cell(cell_width, 8, 'المبلغ المسدد', 1, 0, 'C')
            pdf.rtl_cell(cell_width, 8, 'مبلغ الدين', 1, 0, 'C')
            pdf.rtl_cell(cell_width, 8, 'اسم الطالب', 1, 1, 'C')
            
            # بيانات الجدول
            pdf.set_font('DejaVu', '', 10)
            for _, row in fully_paid_df.iterrows():
                pdf.rtl_cell(cell_width, 8, format_currency(row['Remaining Amount']), 1, 0, 'C')
                pdf.rtl_cell(cell_width, 8, format_currency(row['Paid Amount']), 1, 0, 'C')
                pdf.rtl_cell(cell_width, 8, format_currency(row['Debt Amount']), 1, 0, 'C')
                pdf.rtl_cell(cell_width, 8, row['Student Name'], 1, 1, 'R')
        
        # الطلاب المسددين جزئياً
        if not partially_paid_df.empty:
            pdf.ln(10)
            pdf.set_font('DejaVu', '', 12)
            partially_paid_count = len(partially_paid_df)
            title = 'الطلاب المسددين جزئياً (' + str(partially_paid_count) + ')'
            pdf.rtl_cell(0, 10, title, 0, 1, 'R')
            
            # رؤوس الجدول
            pdf.set_font('DejaVu', '', 10)
            pdf.rtl_cell(30, 8, 'نسبة السداد', 1, 0, 'C')
            pdf.rtl_cell(40, 8, 'المبلغ المتبقي', 1, 0, 'C')
            pdf.rtl_cell(40, 8, 'المبلغ المسدد', 1, 0, 'C')
            pdf.rtl_cell(40, 8, 'مبلغ الدين', 1, 0, 'C')
            pdf.rtl_cell(40, 8, 'اسم الطالب', 1, 1, 'C')
            
            # بيانات الجدول
            pdf.set_font('DejaVu', '', 10)
            for _, row in partially_paid_df.iterrows():
                try:
                    if isinstance(row['Payment Percentage'], str):
                        # إزالة علامة % إذا كانت موجودة
                        clean_percent = row['Payment Percentage'].replace('%', '')
                        payment_percentage = float(clean_percent)
                    else:
                        payment_percentage = float(row['Payment Percentage'])
                    
                    # تكوين سلسلة النص بالعلامة المئوية
                    percentage_text = str(round(payment_percentage, 1)) + "%"
                    pdf.rtl_cell(30, 8, percentage_text, 1, 0, 'C')
                except (ValueError, TypeError):
                    # إذا لم يمكن تحويل القيمة، استخدم "0%"
                    pdf.rtl_cell(30, 8, "0%", 1, 0, 'C')
                pdf.rtl_cell(40, 8, format_currency(row['Remaining Amount']), 1, 0, 'C')
                pdf.rtl_cell(40, 8, format_currency(row['Paid Amount']), 1, 0, 'C')
                pdf.rtl_cell(40, 8, format_currency(row['Debt Amount']), 1, 0, 'C')
                pdf.rtl_cell(40, 8, row['Student Name'], 1, 1, 'R')
        
        # الطلاب غير المسددين
        if not unpaid_df.empty:
            pdf.ln(10)
            pdf.set_font('DejaVu', '', 12)
            unpaid_count = len(unpaid_df)
            title = 'الطلاب غير المسددين (' + str(unpaid_count) + ')'
            pdf.rtl_cell(0, 10, title, 0, 1, 'R')
            
            # رؤوس الجدول
            pdf.set_font('DejaVu', '', 10)
            cell_width = 45
            pdf.rtl_cell(cell_width, 8, 'المبلغ المتبقي', 1, 0, 'C')
            pdf.rtl_cell(cell_width, 8, 'المبلغ المسدد', 1, 0, 'C')
            pdf.rtl_cell(cell_width, 8, 'مبلغ الدين', 1, 0, 'C')
            pdf.rtl_cell(cell_width, 8, 'اسم الطالب', 1, 1, 'C')
            
            # بيانات الجدول
            pdf.set_font('DejaVu', '', 10)
            for _, row in unpaid_df.iterrows():
                pdf.rtl_cell(cell_width, 8, format_currency(row['Remaining Amount']), 1, 0, 'C')
                pdf.rtl_cell(cell_width, 8, format_currency(row['Paid Amount']), 1, 0, 'C')
                pdf.rtl_cell(cell_width, 8, format_currency(row['Debt Amount']), 1, 0, 'C')
                pdf.rtl_cell(cell_width, 8, row['Student Name'], 1, 1, 'R')
        
        return pdf
    
    return create_pdf_buffer(create_content)


def create_download_link(pdf_bytes, filename):
    """إنشاء رابط تنزيل للملف"""
    b64 = base64.b64encode(pdf_bytes.read()).decode()
    href = f'<a href="data:application/pdf;base64,{b64}" download="{filename}" class="download-link">تنزيل الملف</a>'
    return href