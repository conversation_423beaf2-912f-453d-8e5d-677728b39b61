"""
وحدة مبسطة لإنشاء تقارير PDF تتجنب مشاكل الدمج بين الأرقام والنصوص
"""
import os
import base64
import tempfile
from datetime import datetime
import io
import pandas as pd
from fpdf import FPDF
import streamlit as st

from utils import format_currency

class ArabicPDF(FPDF):
    """فئة FPDF معدلة مع دعم أفضل للنصوص العربية"""
    
    def __init__(self):
        # تهيئة بالإعدادات الافتراضية
        super().__init__(orientation='P', unit='mm', format='A4')
        # إضافة خط يدعم العربية - يجب أن يكون الملف في نفس المجلد
        self.add_font('DejaVu', '', os.path.join(os.path.dirname(__file__), 'DejaVuSansCondensed.ttf'), uni=True)
        self.set_auto_page_break(auto=True, margin=15)
        self.add_page()
        self.set_font('DejaVu', '', 14)
    
    def header(self):
        """ترويسة الصفحة"""
        # تعيين الخط للترويسة
        self.set_font('DejaVu', '', 12)
        # العنوان (مركز النص)
        self.cell(0, 10, 'نظام إدارة ديون الطلاب', 0, 0, 'C')
        # تاريخ التقرير (يمين الصفحة)
        self.set_xy(10, 10)
        report_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        self.cell(0, 10, report_date, 0, 0, 'R')
        # الخط الفاصل
        self.line(10, 20, 200, 20)
        self.ln(15)
    
    def footer(self):
        """تذييل الصفحة"""
        # الانتقال إلى موضع 1.5 سم من أسفل الصفحة
        self.set_y(-15)
        # تعيين خط الطباعة
        self.set_font('DejaVu', '', 8)
        # رقم الصفحة (وسط الصفحة)
        page_number = str(self.page_no())
        self.cell(0, 10, 'الصفحة ' + page_number, 0, 0, 'C')
    
    def rtl_cell(self, w, h, txt, border=0, ln=0, align='', fill=False, direction='R'):
        """خلية معدلة للنصوص العربية من اليمين إلى اليسار"""
        # الاتجاه الافتراضي من اليمين إلى اليسار للنص العربي
        if direction == 'R':
            # تغيير محاذاة النص حسب الاتجاه
            if align == '':
                align = 'R'
        
        # استدعاء الدالة cell الأصلية
        self.cell(w, h, txt, border, ln, align, fill)

def create_pdf_buffer(content_function):
    """
    دالة مساعدة لإنشاء كائن ذاكرة PDF بطريقة آمنة باستخدام ملفات مؤقتة
    
    Args:
        content_function: دالة تضيف محتوى إلى كائن PDF
        
    Returns:
        BytesIO: كائن ذاكرة يحتوي على ملف PDF
    """
    try:
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as tmp:
            tmp_path = tmp.name
        
        # إنشاء وتعبئة محتوى PDF
        pdf = content_function()
        
        # حفظ PDF في الملف المؤقت
        pdf.output(tmp_path)
        
        # إعادة قراءة الملف كبيانات ثنائية
        with open(tmp_path, 'rb') as f:
            pdf_data = io.BytesIO(f.read())
        
        # حذف الملف المؤقت
        os.unlink(tmp_path)
        
        # إعادة البيانات الثنائية
        pdf_data.seek(0)
        return pdf_data
        
    except Exception as e:
        st.error(f"خطأ في إنشاء ملف PDF: {str(e)}")
        return None

def export_all_students_pdf(df):
    """
    تصدير بيانات جميع الطلاب إلى ملف PDF
    
    Args:
        df (DataFrame): بيانات الطلاب
        
    Returns:
        BytesIO: كائن ذاكرة يحتوي على ملف PDF
    """
    def create_content():
        # إنشاء كائن PDF جديد
        pdf = ArabicPDF()
        
        # العنوان
        pdf.set_font('DejaVu', '', 16)
        pdf.rtl_cell(0, 10, 'تقرير جميع الطلاب', 0, 1, 'C')
        pdf.ln(5)
        
        # معلومات إجمالية
        pdf.set_font('DejaVu', '', 12)
        pdf.rtl_cell(0, 10, 'ملخص', 0, 1, 'R')
        
        pdf.set_font('DejaVu', '', 10)
        
        # حساب المجاميع
        total_debt = df['Debt Amount'].sum()
        total_paid = df['Paid Amount'].sum()
        total_remaining = df['Remaining Amount'].sum()
        
        # عرض المجاميع
        pdf.rtl_cell(70, 8, 'إجمالي مبلغ الدين:', 0, 0, 'R')
        pdf.rtl_cell(0, 8, format_currency(total_debt), 0, 1, 'R')
        
        pdf.rtl_cell(70, 8, 'إجمالي المبلغ المسدد:', 0, 0, 'R')
        pdf.rtl_cell(0, 8, format_currency(total_paid), 0, 1, 'R')
        
        pdf.rtl_cell(70, 8, 'إجمالي المبلغ المتبقي:', 0, 0, 'R')
        pdf.rtl_cell(0, 8, format_currency(total_remaining), 0, 1, 'R')
        
        # رؤوس الجدول
        pdf.ln(10)
        pdf.set_font('DejaVu', '', 10)
        cell_width = 30
        pdf.rtl_cell(30, 8, 'نسبة السداد', 1, 0, 'C')
        pdf.rtl_cell(cell_width, 8, 'المبلغ المتبقي', 1, 0, 'C')
        pdf.rtl_cell(cell_width, 8, 'المبلغ المسدد', 1, 0, 'C')
        pdf.rtl_cell(cell_width, 8, 'مبلغ الدين', 1, 0, 'C')
        pdf.rtl_cell(cell_width, 8, 'اسم الطالب', 1, 1, 'C')
        
        # بيانات الجدول
        pdf.set_font('DejaVu', '', 10)
        for _, row in df.iterrows():
            # حساب نسبة السداد
            debt_amount = float(row['Debt Amount']) if row['Debt Amount'] else 0
            paid_amount = float(row['Paid Amount']) if row['Paid Amount'] else 0
            
            # تجنب القسمة على الصفر
            if debt_amount > 0:
                payment_percentage = (paid_amount / debt_amount) * 100
            else:
                payment_percentage = 0
                
            # طباعة بيانات الطالب
            pdf.rtl_cell(30, 8, str(round(payment_percentage, 1)) + "%", 1, 0, 'C')
            pdf.rtl_cell(cell_width, 8, format_currency(row['Remaining Amount']), 1, 0, 'C')
            pdf.rtl_cell(cell_width, 8, format_currency(row['Paid Amount']), 1, 0, 'C')
            pdf.rtl_cell(cell_width, 8, format_currency(row['Debt Amount']), 1, 0, 'C')
            pdf.rtl_cell(cell_width, 8, str(row['Student Name']), 1, 1, 'R')
        
        return pdf
    
    return create_pdf_buffer(create_content)

def export_student_report_pdf(student_name, debt_data, transactions_df):
    """
    تصدير تقرير تفصيلي لطالب معين إلى ملف PDF
    
    Args:
        student_name (str): اسم الطالب
        debt_data (dict): بيانات دين الطالب
        transactions_df (DataFrame): سجل معاملات الطالب
        
    Returns:
        BytesIO: كائن ذاكرة يحتوي على ملف PDF
    """
    def create_content():
        # إنشاء كائن PDF جديد
        pdf = ArabicPDF()
        
        # العنوان
        pdf.set_font('DejaVu', '', 16)
        title = 'تقرير الطالب: ' + str(student_name)
        pdf.rtl_cell(0, 10, title, 0, 1, 'C')
        pdf.ln(5)
        
        # معلومات الدين
        pdf.set_font('DejaVu', '', 12)
        pdf.rtl_cell(0, 10, 'معلومات الدين', 0, 1, 'R')
        
        # استخراج البيانات
        debt_amount = debt_data.get('debt_amount', 0)
        paid_amount = debt_data.get('paid_amount', 0)
        remaining_amount = debt_amount - paid_amount
        
        # حساب نسبة السداد
        payment_percentage = (paid_amount / debt_amount) * 100 if debt_amount > 0 else 0
        
        # عرض البيانات
        pdf.set_font('DejaVu', '', 10)
        pdf.rtl_cell(70, 8, 'مبلغ الدين:', 0, 0, 'R')
        pdf.rtl_cell(0, 8, format_currency(debt_amount), 0, 1, 'R')
        
        pdf.rtl_cell(70, 8, 'المبلغ المسدد:', 0, 0, 'R')
        pdf.rtl_cell(0, 8, format_currency(paid_amount), 0, 1, 'R')
        
        pdf.rtl_cell(70, 8, 'حالة السداد:', 0, 0, 'R')
        
        # تحديد حالة السداد
        if payment_percentage >= 100:
            payment_status = "مسدد بالكامل"
        elif payment_percentage > 0:
            payment_status = "مسدد جزئياً"
        else:
            payment_status = "غير مسدد"
        
        pdf.rtl_cell(0, 8, payment_status, 0, 1, 'R')
        
        pdf.rtl_cell(70, 8, 'المبلغ المتبقي:', 0, 0, 'R')
        pdf.rtl_cell(0, 8, format_currency(remaining_amount), 0, 1, 'R')
        
        pdf.rtl_cell(70, 8, 'نسبة السداد:', 0, 0, 'R')
        percentage_text = str(round(payment_percentage, 1)) + "%"
        pdf.rtl_cell(0, 8, percentage_text, 0, 1, 'R')
        
        # عرض سجل المعاملات إذا كان موجودًا
        if not transactions_df.empty:
            pdf.ln(10)
            pdf.set_font('DejaVu', '', 12)
            pdf.rtl_cell(0, 10, 'سجل المعاملات', 0, 1, 'R')
            
            # رؤوس الجدول
            pdf.set_font('DejaVu', '', 10)
            pdf.rtl_cell(50, 8, 'ملاحظات', 1, 0, 'C')
            pdf.rtl_cell(30, 8, 'المبلغ', 1, 0, 'C')
            pdf.rtl_cell(40, 8, 'نوع المعاملة', 1, 0, 'C')
            pdf.rtl_cell(40, 8, 'التاريخ', 1, 1, 'C')
            
            # بيانات المعاملات
            pdf.set_font('DejaVu', '', 10)
            for _, row in transactions_df.iterrows():
                transaction_type = 'دفع' if row['Transaction Type'] == 'payment' else 'دين'
                
                # تنسيق التاريخ
                if isinstance(row['Transaction Date'], str):
                    date_str = row['Transaction Date']
                else:
                    date_str = row['Transaction Date'].strftime('%Y-%m-%d') if row['Transaction Date'] else ''
                
                notes = str(row['Notes']) if row['Notes'] else ''
                
                pdf.rtl_cell(50, 8, notes, 1, 0, 'R')
                pdf.rtl_cell(30, 8, format_currency(row['Amount']), 1, 0, 'C')
                pdf.rtl_cell(40, 8, transaction_type, 1, 0, 'C')
                pdf.rtl_cell(40, 8, date_str, 1, 1, 'C')
        
        return pdf
    
    return create_pdf_buffer(create_content)

def export_payment_status_report_pdf(fully_paid_df, partially_paid_df, unpaid_df):
    """
    تصدير تقرير حالة السداد للطلاب إلى ملف PDF.
    يعرض قوائم للطلاب المسددين بالكامل، المسددين جزئياً، وغير المسددين
    
    Args:
        fully_paid_df (DataFrame): إطار بيانات الطلاب المسددين بالكامل
        partially_paid_df (DataFrame): إطار بيانات الطلاب المسددين جزئياً
        unpaid_df (DataFrame): إطار بيانات الطلاب غير المسددين
        
    Returns:
        BytesIO: كائن ذاكرة يحتوي على ملف PDF
    """
    def create_content():
        # إنشاء كائن PDF جديد
        pdf = ArabicPDF()
        
        # العنوان
        pdf.set_font('DejaVu', '', 16)
        pdf.rtl_cell(0, 10, 'تقرير حالة سداد الطلاب', 0, 1, 'C')
        pdf.ln(5)
        
        # معلومات إجمالية
        pdf.set_font('DejaVu', '', 12)
        pdf.rtl_cell(0, 10, 'ملخص', 0, 1, 'R')
        
        pdf.set_font('DejaVu', '', 10)
        # تأكد من التعامل مع الأرقام على هيئة نصوص
        pdf.rtl_cell(70, 8, 'الطلاب المسددين بالكامل:', 0, 0, 'R')
        pdf.rtl_cell(0, 8, str(len(fully_paid_df)), 0, 1, 'R')
        
        pdf.rtl_cell(70, 8, 'الطلاب المسددين جزئياً:', 0, 0, 'R')
        pdf.rtl_cell(0, 8, str(len(partially_paid_df)), 0, 1, 'R')
        
        pdf.rtl_cell(70, 8, 'الطلاب غير المسددين:', 0, 0, 'R')
        pdf.rtl_cell(0, 8, str(len(unpaid_df)), 0, 1, 'R')
        
        total_students = len(fully_paid_df) + len(partially_paid_df) + len(unpaid_df)
        pdf.rtl_cell(70, 8, 'إجمالي عدد الطلاب:', 0, 0, 'R')
        pdf.rtl_cell(0, 8, str(total_students), 0, 1, 'R')
        
        # الطلاب المسددين بالكامل
        if not fully_paid_df.empty:
            pdf.ln(10)
            pdf.set_font('DejaVu', '', 12)
            fully_paid_count = len(fully_paid_df)
            title = 'الطلاب المسددين بالكامل (' + str(fully_paid_count) + ')'
            pdf.rtl_cell(0, 10, title, 0, 1, 'R')
            
            # رؤوس الجدول
            pdf.set_font('DejaVu', '', 10)
            cell_width = 45
            pdf.rtl_cell(cell_width, 8, 'المبلغ المتبقي', 1, 0, 'C')
            pdf.rtl_cell(cell_width, 8, 'المبلغ المسدد', 1, 0, 'C')
            pdf.rtl_cell(cell_width, 8, 'مبلغ الدين', 1, 0, 'C')
            pdf.rtl_cell(cell_width, 8, 'اسم الطالب', 1, 1, 'C')
            
            # بيانات الجدول
            pdf.set_font('DejaVu', '', 10)
            for _, row in fully_paid_df.iterrows():
                pdf.rtl_cell(cell_width, 8, format_currency(row['Remaining Amount']), 1, 0, 'C')
                pdf.rtl_cell(cell_width, 8, format_currency(row['Paid Amount']), 1, 0, 'C')
                pdf.rtl_cell(cell_width, 8, format_currency(row['Debt Amount']), 1, 0, 'C')
                pdf.rtl_cell(cell_width, 8, str(row['Student Name']), 1, 1, 'R')
        
        # الطلاب المسددين جزئياً
        if not partially_paid_df.empty:
            pdf.ln(10)
            pdf.set_font('DejaVu', '', 12)
            partially_paid_count = len(partially_paid_df)
            title = 'الطلاب المسددين جزئياً (' + str(partially_paid_count) + ')'
            pdf.rtl_cell(0, 10, title, 0, 1, 'R')
            
            # رؤوس الجدول
            pdf.set_font('DejaVu', '', 10)
            pdf.rtl_cell(30, 8, 'نسبة السداد', 1, 0, 'C')
            pdf.rtl_cell(40, 8, 'المبلغ المتبقي', 1, 0, 'C')
            pdf.rtl_cell(40, 8, 'المبلغ المسدد', 1, 0, 'C')
            pdf.rtl_cell(40, 8, 'مبلغ الدين', 1, 0, 'C')
            pdf.rtl_cell(40, 8, 'اسم الطالب', 1, 1, 'C')
            
            # بيانات الجدول
            pdf.set_font('DejaVu', '', 10)
            for _, row in partially_paid_df.iterrows():
                try:
                    if isinstance(row['Payment Percentage'], str):
                        # إزالة علامة % إذا كانت موجودة
                        clean_percent = row['Payment Percentage'].replace('%', '')
                        payment_percentage = float(clean_percent)
                    else:
                        payment_percentage = float(row['Payment Percentage'])
                    
                    # تكوين سلسلة النص بالعلامة المئوية
                    percentage_text = str(round(payment_percentage, 1)) + "%"
                    pdf.rtl_cell(30, 8, percentage_text, 1, 0, 'C')
                except (ValueError, TypeError):
                    # إذا لم يمكن تحويل القيمة، استخدم "0%"
                    pdf.rtl_cell(30, 8, "0%", 1, 0, 'C')
                pdf.rtl_cell(40, 8, format_currency(row['Remaining Amount']), 1, 0, 'C')
                pdf.rtl_cell(40, 8, format_currency(row['Paid Amount']), 1, 0, 'C')
                pdf.rtl_cell(40, 8, format_currency(row['Debt Amount']), 1, 0, 'C')
                pdf.rtl_cell(40, 8, str(row['Student Name']), 1, 1, 'R')
        
        # الطلاب غير المسددين
        if not unpaid_df.empty:
            pdf.ln(10)
            pdf.set_font('DejaVu', '', 12)
            unpaid_count = len(unpaid_df)
            title = 'الطلاب غير المسددين (' + str(unpaid_count) + ')'
            pdf.rtl_cell(0, 10, title, 0, 1, 'R')
            
            # رؤوس الجدول
            pdf.set_font('DejaVu', '', 10)
            cell_width = 45
            pdf.rtl_cell(cell_width, 8, 'المبلغ المتبقي', 1, 0, 'C')
            pdf.rtl_cell(cell_width, 8, 'المبلغ المسدد', 1, 0, 'C')
            pdf.rtl_cell(cell_width, 8, 'مبلغ الدين', 1, 0, 'C')
            pdf.rtl_cell(cell_width, 8, 'اسم الطالب', 1, 1, 'C')
            
            # بيانات الجدول
            pdf.set_font('DejaVu', '', 10)
            for _, row in unpaid_df.iterrows():
                pdf.rtl_cell(cell_width, 8, format_currency(row['Remaining Amount']), 1, 0, 'C')
                pdf.rtl_cell(cell_width, 8, format_currency(row['Paid Amount']), 1, 0, 'C')
                pdf.rtl_cell(cell_width, 8, format_currency(row['Debt Amount']), 1, 0, 'C')
                pdf.rtl_cell(cell_width, 8, str(row['Student Name']), 1, 1, 'R')
        
        return pdf
    
    return create_pdf_buffer(create_content)

def create_download_link(pdf_bytes, filename):
    """إنشاء رابط تنزيل للملف"""
    if pdf_bytes is None:
        return "فشل إنشاء الملف"
    
    b64 = base64.b64encode(pdf_bytes.read()).decode()
    href = '<a href="data:application/pdf;base64,' + b64 + '" download="' + filename + '" class="download-link">تنزيل الملف</a>'
    return href