"""
وحدة إصدار سندات القبض والفواتير
تحتوي على الوظائف المستخدمة لإنشاء وتنسيق وطباعة سندات القبض والفواتير
"""

import io
import datetime
import pandas as pd
from utils import format_currency

def create_receipt_html(student_name, receipt_amount, debt_amount, paid_amount, receipt_date, receipt_notes, receipt_payment_method):
    """
    إنشاء HTML لسند قبض
    
    Args:
        student_name (str): اسم الطالب
        receipt_amount (float): المبلغ المستلم
        debt_amount (float): إجمالي قيمة الدين
        paid_amount (float): المبلغ المسدد سابقاً
        receipt_date (datetime.date): تاريخ الاستلام
        receipt_notes (str): ملاحظات
        receipt_payment_method (str): طريقة الدفع
        
    Returns:
        str: كود HTML لسند القبض مع كود JavaScript للطباعة
    """
    receipt_timestamp = int(datetime.datetime.now().timestamp())
    remaining_after_payment = debt_amount - paid_amount - receipt_amount
    
    receipt_html = f"""
    <div id="receipt-container-{receipt_timestamp}" style="display: none;">
        <div style="font-family: Arial, sans-serif; direction: rtl; padding: 20px; max-width: 800px; margin: 0 auto; border: 1px solid #ddd;">
            <div style="text-align: center; margin-bottom: 20px;">
                <h1 style="color: #1976D2; margin-bottom: 5px;">نظام إدارة ديون الطلاب</h1>
                <h2>سند قبض</h2>
                <p style="color: #666;">رقم السند: REC-{receipt_timestamp}</p>
            </div>
            
            <div style="margin-bottom: 30px; padding: 15px; background-color: #f9f9f9; border-radius: 4px;">
                <h3 style="margin-top: 0; color: #333;">بيانات الطالب والدفع</h3>
                <p><strong>اسم الطالب:</strong> {student_name}</p>
                <p><strong>تاريخ الدفع:</strong> {receipt_date.strftime('%Y-%m-%d')}</p>
                <p><strong>طريقة الدفع:</strong> {receipt_payment_method}</p>
                <p><strong>المبلغ المستلم:</strong> {format_currency(receipt_amount)}</p>
            </div>
            
            <div style="margin-bottom: 30px; padding: 15px; background-color: #f9f9f9; border-radius: 4px;">
                <h3 style="margin-top: 0; color: #333;">تفاصيل المديونية</h3>
                <p><strong>قيمة الدين الإجمالية:</strong> {format_currency(debt_amount)}</p>
                <p><strong>المبلغ المسدد سابقاً:</strong> {format_currency(paid_amount)}</p>
                <p><strong>المبلغ المسدد حالياً:</strong> {format_currency(receipt_amount)}</p>
                <p><strong>المبلغ المتبقي:</strong> {format_currency(remaining_after_payment)}</p>
            </div>
            
            <div style="margin-bottom: 30px;">
                <h3 style="color: #333;">ملاحظات</h3>
                <p>{receipt_notes}</p>
            </div>
            
            <div style="display: flex; justify-content: space-between; margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd;">
                <div style="text-align: center; width: 40%;">
                    <p style="margin-bottom: 40px;">توقيع المستلم</p>
                    <div style="border-top: 1px solid #000; width: 100%;"></div>
                </div>
                <div style="text-align: center; width: 40%;">
                    <p style="margin-bottom: 40px;">توقيع الطالب</p>
                    <div style="border-top: 1px solid #000; width: 100%;"></div>
                </div>
            </div>
            
            <div style="margin-top: 40px; text-align: center; color: #666; font-size: 14px;">
                <p>تم إصدار هذا السند بتاريخ {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>
        </div>
    </div>
    
    <script>
        // إنشاء طباعة سند القبض
        (function() {{
            var printWindow = window.open('', '_blank');
            if (!printWindow) {{
                alert('يرجى السماح بالنوافذ المنبثقة لطباعة سند القبض');
                return;
            }}
            
            // نسخ محتوى السند إلى النافذة الجديدة
            var receiptContent = document.getElementById('receipt-container-{receipt_timestamp}').innerHTML;
            
            printWindow.document.write(`
                <html dir="rtl">
                <head>
                    <title>سند قبض - {student_name}</title>
                    <meta charset="UTF-8">
                    <style>
                        body {{ 
                            font-family: Arial, sans-serif; 
                            direction: rtl; 
                            padding: 0;
                            margin: 0;
                        }}
                        @media print {{
                            body {{ padding: 0; margin: 0; }}
                        }}
                    </style>
                </head>
                <body>${{receiptContent}}</body>
                </html>
            `);
            
            printWindow.document.close();
            
            // طباعة النافذة بعد تحميلها
            printWindow.onload = function() {{
                setTimeout(function() {{
                    printWindow.print();
                    printWindow.onafterprint = function() {{
                        printWindow.close();
                    }};
                }}, 500);
            }};
        }})();
    </script>
    """
    return receipt_html

def create_invoice_html(invoice_type, student_name, student_debt, student_paid, period_total, date_label, transactions_df):
    """
    إنشاء HTML لفاتورة
    
    Args:
        invoice_type (str): نوع الفاتورة (فاتورة دين أو فاتورة تسديد)
        student_name (str): اسم الطالب
        student_debt (float): إجمالي قيمة الدين
        student_paid (float): المبلغ المسدد
        period_total (float): إجمالي المبلغ خلال الفترة
        date_label (str): عنوان الفترة الزمنية
        transactions_df (DataFrame): إطار بيانات المعاملات
        
    Returns:
        str: كود HTML للفاتورة مع JavaScript للطباعة
    """
    invoice_timestamp = int(datetime.datetime.now().timestamp())
    student_remaining = student_debt - student_paid
    
    # تحديد العنوان بناءً على نوع الفاتورة
    transaction_type_label = "إجمالي الديون" if invoice_type == "فاتورة دين" else "إجمالي التسديدات"
    
    invoice_html = f"""
    <div id="invoice-container-{invoice_timestamp}" style="display: none;">
        <div style="font-family: Arial, sans-serif; direction: rtl; padding: 20px; max-width: 800px; margin: 0 auto; border: 1px solid #ddd;">
            <div style="text-align: center; margin-bottom: 20px;">
                <h1 style="color: #1976D2; margin-bottom: 5px;">نظام إدارة ديون الطلاب</h1>
                <h2>{invoice_type}</h2>
                <p style="color: #666;">{date_label}</p>
            </div>
            
            <div style="margin-bottom: 30px; padding: 15px; background-color: #f9f9f9; border-radius: 4px;">
                <h3 style="margin-top: 0; color: #333;">معلومات الطالب</h3>
                <p><strong>اسم الطالب:</strong> {student_name}</p>
                <p><strong>قيمة الدين الإجمالية:</strong> {format_currency(student_debt)}</p>
                <p><strong>المبلغ المسدد الإجمالي:</strong> {format_currency(student_paid)}</p>
                <p><strong>المبلغ المتبقي:</strong> {format_currency(student_remaining)}</p>
            </div>
            
            <div style="margin-bottom: 30px;">
                <h3 style="color: #333;">تفاصيل {invoice_type}</h3>
                <p><strong>{transaction_type_label} خلال الفترة:</strong> {format_currency(period_total)}</p>
            </div>
            
            <table style="width: 100%; border-collapse: collapse; margin-bottom: 30px;">
                <thead>
                    <tr style="background-color: #f2f2f2;">
                        <th style="padding: 12px; text-align: right; border: 1px solid #ddd;">نوع المعاملة</th>
                        <th style="padding: 12px; text-align: right; border: 1px solid #ddd;">المبلغ</th>
                        <th style="padding: 12px; text-align: right; border: 1px solid #ddd;">تاريخ المعاملة</th>
                        <th style="padding: 12px; text-align: right; border: 1px solid #ddd;">ملاحظات</th>
                    </tr>
                </thead>
                <tbody>
    """
    
    # إضافة صفوف الجدول من البيانات
    if not transactions_df.empty:
        # تحضير البيانات
        display_df = transactions_df.copy()
        display_df['Amount'] = display_df['Amount'].apply(format_currency)
        display_df['Transaction Date'] = display_df['Transaction Date'].dt.strftime('%Y-%m-%d %H:%M')
        
        # إعادة تسمية الأعمدة بالعربية
        display_df = display_df.rename(columns={
            'Transaction Type': 'نوع المعاملة',
            'Amount': 'المبلغ',
            'Transaction Date': 'تاريخ المعاملة',
            'Notes': 'ملاحظات'
        })
        
        # ترجمة قيم نوع المعاملة
        display_df['نوع المعاملة'] = display_df['نوع المعاملة'].replace({
            'debt': 'دين',
            'payment': 'تسديد'
        })
        
        # إضافة الصفوف إلى HTML
        for idx, row in display_df.iterrows():
            invoice_html += f"""
            <tr>
                <td style="padding: 12px; text-align: right; border: 1px solid #ddd;">{row['نوع المعاملة']}</td>
                <td style="padding: 12px; text-align: right; border: 1px solid #ddd;">{row['المبلغ']}</td>
                <td style="padding: 12px; text-align: right; border: 1px solid #ddd;">{row['تاريخ المعاملة']}</td>
                <td style="padding: 12px; text-align: right; border: 1px solid #ddd;">{row['ملاحظات'] if not pd.isna(row['ملاحظات']) else ''}</td>
            </tr>
            """
    
    # إكمال HTML الفاتورة
    invoice_html += f"""
                </tbody>
            </table>
            
            <div style="margin-top: 40px; text-align: center; color: #666; font-size: 14px;">
                <p>تم إصدار هذه الفاتورة بتاريخ {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>
        </div>
    </div>
    
    <script>
        // إنشاء طباعة الفاتورة
        (function() {{
            var printWindow = window.open('', '_blank');
            if (!printWindow) {{
                alert('يرجى السماح بالنوافذ المنبثقة لطباعة الفاتورة');
                return;
            }}
            
            // نسخ محتوى الفاتورة إلى النافذة الجديدة
            var invoiceContent = document.getElementById('invoice-container-{invoice_timestamp}').innerHTML;
            
            printWindow.document.write(`
                <html dir="rtl">
                <head>
                    <title>{invoice_type} - {student_name}</title>
                    <meta charset="UTF-8">
                    <style>
                        body {{ 
                            font-family: Arial, sans-serif; 
                            direction: rtl; 
                            padding: 0;
                            margin: 0;
                        }}
                        @media print {{
                            body {{ padding: 0; margin: 0; }}
                        }}
                    </style>
                </head>
                <body>${{invoiceContent}}</body>
                </html>
            `);
            
            printWindow.document.close();
            
            // طباعة النافذة بعد تحميلها
            printWindow.onload = function() {{
                setTimeout(function() {{
                    printWindow.print();
                    printWindow.onafterprint = function() {{
                        printWindow.close();
                    }};
                }}, 500);
            }};
        }})();
    </script>
    """
    
    return invoice_html

def create_bulk_invoice_report_html(report_title, bulk_date_label, total_amount, unique_students, transaction_count, transactions_df):
    """
    إنشاء HTML لتقرير فواتير متعددة
    
    Args:
        report_title (str): عنوان التقرير
        bulk_date_label (str): عنوان الفترة الزمنية
        total_amount (float): إجمالي المبالغ
        unique_students (int): عدد الطلاب الفريدين
        transaction_count (int): عدد المعاملات
        transactions_df (DataFrame): إطار بيانات المعاملات
        
    Returns:
        str: كود HTML للتقرير مع JavaScript للطباعة
    """
    report_timestamp = int(datetime.datetime.now().timestamp())
    
    report_html = f"""
    <div id="report-container-{report_timestamp}" style="display: none;">
        <div style="font-family: Arial, sans-serif; direction: rtl; padding: 20px; max-width: 800px; margin: 0 auto; border: 1px solid #ddd;">
            <div style="text-align: center; margin-bottom: 20px;">
                <h1 style="color: #1976D2; margin-bottom: 5px;">نظام إدارة ديون الطلاب</h1>
                <h2>{report_title}</h2>
                <p style="color: #666;">{bulk_date_label}</p>
            </div>
            
            <div style="margin-bottom: 30px; padding: 15px; background-color: #f9f9f9; border-radius: 4px;">
                <h3 style="margin-top: 0; color: #333;">ملخص التقرير</h3>
                <p><strong>عدد الطلاب:</strong> {unique_students}</p>
                <p><strong>عدد المعاملات:</strong> {transaction_count}</p>
                <p><strong>إجمالي المبالغ:</strong> {format_currency(total_amount)}</p>
            </div>
            
            <h3 style="color: #333;">تفاصيل المعاملات</h3>
            
            <table style="width: 100%; border-collapse: collapse; margin-bottom: 30px;">
                <thead>
                    <tr style="background-color: #f2f2f2;">
                        <th style="padding: 12px; text-align: right; border: 1px solid #ddd;">اسم الطالب</th>
                        <th style="padding: 12px; text-align: right; border: 1px solid #ddd;">نوع المعاملة</th>
                        <th style="padding: 12px; text-align: right; border: 1px solid #ddd;">المبلغ</th>
                        <th style="padding: 12px; text-align: right; border: 1px solid #ddd;">تاريخ المعاملة</th>
                        <th style="padding: 12px; text-align: right; border: 1px solid #ddd;">ملاحظات</th>
                    </tr>
                </thead>
                <tbody>
    """
    
    # إضافة صفوف الجدول من البيانات
    if not transactions_df.empty:
        # تحضير البيانات
        display_df = transactions_df.copy()
        
        # إضافة الصفوف إلى HTML
        for idx, row in display_df.iterrows():
            notes_value = row['ملاحظات'] if not pd.isna(row['ملاحظات']) else ''
            
            report_html += f"""
            <tr>
                <td style="padding: 12px; text-align: right; border: 1px solid #ddd;">{row['اسم الطالب']}</td>
                <td style="padding: 12px; text-align: right; border: 1px solid #ddd;">{row['نوع المعاملة']}</td>
                <td style="padding: 12px; text-align: right; border: 1px solid #ddd;">{row['المبلغ']}</td>
                <td style="padding: 12px; text-align: right; border: 1px solid #ddd;">{row['تاريخ المعاملة']}</td>
                <td style="padding: 12px; text-align: right; border: 1px solid #ddd;">{notes_value}</td>
            </tr>
            """
    
    # إكمال HTML التقرير
    report_html += f"""
                </tbody>
            </table>
            
            <div style="margin-top: 40px; text-align: center; color: #666; font-size: 14px;">
                <p>تم إصدار هذا التقرير بتاريخ {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>
        </div>
    </div>
    
    <script>
        // إنشاء طباعة التقرير
        (function() {{
            var printWindow = window.open('', '_blank');
            if (!printWindow) {{
                alert('يرجى السماح بالنوافذ المنبثقة لطباعة التقرير');
                return;
            }}
            
            // نسخ محتوى التقرير إلى النافذة الجديدة
            var reportContent = document.getElementById('report-container-{report_timestamp}').innerHTML;
            
            printWindow.document.write(`
                <html dir="rtl">
                <head>
                    <title>{report_title}</title>
                    <meta charset="UTF-8">
                    <style>
                        body {{ 
                            font-family: Arial, sans-serif; 
                            direction: rtl; 
                            padding: 0;
                            margin: 0;
                        }}
                        @media print {{
                            body {{ padding: 0; margin: 0; }}
                        }}
                    </style>
                </head>
                <body>${{reportContent}}</body>
                </html>
            `);
            
            printWindow.document.close();
            
            // طباعة النافذة بعد تحميلها
            printWindow.onload = function() {{
                setTimeout(function() {{
                    printWindow.print();
                    printWindow.onafterprint = function() {{
                        printWindow.close();
                    }};
                }}, 500);
            }};
        }})();
    </script>
    """
    
    return report_html

def export_transactions_to_excel(transactions_df, student_name=None):
    """
    تصدير المعاملات إلى ملف Excel
    
    Args:
        transactions_df (DataFrame): إطار بيانات المعاملات
        student_name (str, optional): اسم الطالب إذا كان التصدير لطالب معين
        
    Returns:
        BytesIO: كائن ذاكرة يحتوي على ملف Excel
    """
    if transactions_df.empty:
        return None
    
    # تنسيق البيانات للتصدير
    export_df = transactions_df.copy()
    
    # تنسيق التاريخ
    if 'Transaction Date' in export_df.columns:
        export_df['Transaction Date'] = export_df['Transaction Date'].dt.strftime('%Y-%m-%d %H:%M:%S')
    
    # إعادة تسمية الأعمدة بالعربية إذا كانت بالإنجليزية
    rename_columns = {
        'Transaction Type': 'نوع المعاملة',
        'Amount': 'المبلغ',
        'Transaction Date': 'تاريخ المعاملة',
        'Notes': 'ملاحظات',
        'Student Name': 'اسم الطالب',
    }
    
    # استبدال أسماء الأعمدة الموجودة فقط
    columns_to_rename = {k: v for k, v in rename_columns.items() if k in export_df.columns}
    if columns_to_rename:
        export_df = export_df.rename(columns=columns_to_rename)
    
    # ترجمة قيم نوع المعاملة
    if 'نوع المعاملة' in export_df.columns:
        export_df['نوع المعاملة'] = export_df['نوع المعاملة'].replace({
            'debt': 'دين',
            'payment': 'تسديد'
        })
    
    # إنشاء ملف Excel في الذاكرة
    excel_buffer = io.BytesIO()
    export_df.to_excel(excel_buffer, index=False)
    excel_buffer.seek(0)
    
    return excel_buffer