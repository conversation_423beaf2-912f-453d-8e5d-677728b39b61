"""
Módulo para la página de dashboard principal.
Proporciona funciones para mostrar el dashboard con métricas y gráficos.
"""

import streamlit as st
import pandas as pd
import plotly.express as px

from ui_components import metric_card, info_box
from utils import format_currency

def show_dashboard(debt_manager):
    """
    Muestra el dashboard principal con métricas y gráficos

    Args:
        debt_manager: Instancia de DatabaseManager para acceder a los datos
    """
    st.header("لوحة المعلومات الرئيسية")

    # Obtener datos
    if debt_manager.is_empty():
        info_box("لا توجد بيانات متاحة. يرجى إضافة بعض الديون أولاً.", "warning")
        return

    df = debt_manager.get_dataframe()

    # Calcular métricas clave
    total_debt = df['Debt Amount'].sum()
    total_paid = df['Paid Amount'].sum()
    total_remaining = df['Remaining Amount'].sum()
    payment_ratio = (total_paid / total_debt * 100) if total_debt > 0 else 0

    # Mostrar métricas en tarjetas modernas
    st.markdown("### الإحصائيات الرئيسية")

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        metric_card("إجمالي عدد الطلاب", len(df))

    with col2:
        metric_card("إجمالي الديون", format_currency(total_debt))

    with col3:
        metric_card("إجمالي المسدد", format_currency(total_paid))

    with col4:
        metric_card("نسبة السداد", f"{payment_ratio:.1f}", suffix="%")

    # Mostrar gráficos
    st.markdown("### التحليل البياني")

    chart_col1, chart_col2 = st.columns(2)

    with chart_col1:
        st.markdown("#### نسبة المبالغ المسددة وغير المسددة")
        fig_pie = create_enhanced_debt_visualization(df)
        st.plotly_chart(fig_pie, use_container_width=True)

    with chart_col2:
        st.markdown("#### توزيع الطلاب حسب حالة السداد")
        fig_dist = create_enhanced_payment_status_chart(df)
        st.plotly_chart(fig_dist, use_container_width=True)

    # Mostrar tabla de estudiantes recientes y con pagos pendientes
    col1, col2 = st.columns(2)

    with col1:
        st.markdown("### آخر الطلاب المضافين")
        if not df.empty:
            # Ordenar por ID (asumiendo que los más recientes tienen ID más alto)
            recent_students = df.sort_values('Student Name').tail(5)
            st.dataframe(
                recent_students[['Student Name', 'Debt Amount', 'Paid Amount', 'Remaining Amount']],
                use_container_width=True
            )
        else:
            st.info("لا توجد بيانات للطلاب")

    with col2:
        st.markdown("### الطلاب ذوو أعلى ديون متبقية")
        if not df.empty:
            # Ordenar por monto restante (de mayor a menor)
            top_remaining = df.sort_values('Remaining Amount', ascending=False).head(5)
            st.dataframe(
                top_remaining[['Student Name', 'Debt Amount', 'Paid Amount', 'Remaining Amount']],
                use_container_width=True
            )
        else:
            st.info("لا توجد بيانات للطلاب")

def create_enhanced_debt_visualization(df):
    """
    Crea una visualización mejorada de la distribución de deudas

    Args:
        df: DataFrame con los datos de deudas

    Returns:
        fig: Figura de Plotly con el gráfico
    """
    # Calcular totales
    total_debt = df['Debt Amount'].sum()
    total_paid = df['Paid Amount'].sum()
    total_remaining = total_debt - total_paid

    # Crear datos para el gráfico
    labels = ['المبلغ المسدد', 'المبلغ المتبقي']
    values = [total_paid, total_remaining]
    colors = ['#4CAF50', '#FF5722']

    # Crear gráfico de pastel interactivo
    fig = px.pie(
        names=labels,
        values=values,
        color_discrete_sequence=colors,
        hole=0.4,
        title="نسبة المبالغ المسددة وغير المسددة"
    )

    # Personalizar diseño
    fig.update_layout(
        margin=dict(t=30, b=0, l=0, r=0),
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=-0.2,
            xanchor="center",
            x=0.5
        ),
        annotations=[dict(
            text=f'<b>{(total_paid/total_debt*100):.1f}%</b><br>مسدد',
            x=0.5, y=0.5,
            font_size=16,
            showarrow=False
        )]
    )

    return fig

def create_enhanced_payment_status_chart(df):
    """
    Crea un gráfico mejorado de estado de pago por estudiante

    Args:
        df: DataFrame con los datos de deudas

    Returns:
        fig: Figura de Plotly con el gráfico
    """
    # Calcular porcentaje de pago para cada estudiante
    df['Payment Percentage'] = (df['Paid Amount'] / df['Debt Amount'] * 100).fillna(0)

    # Categorizar estudiantes
    payment_bins = [0, 0.1, 50, 99.9, 100]
    payment_labels = ['غير مسدد', 'مسدد جزئياً (أقل من 50%)', 'مسدد جزئياً (أكثر من 50%)', 'مسدد بالكامل']

    df['Payment Status'] = pd.cut(
        df['Payment Percentage'],
        bins=payment_bins,
        labels=payment_labels
    )

    # Contar estudiantes por categoría
    status_counts = df['Payment Status'].value_counts().reset_index()
    status_counts.columns = ['حالة السداد', 'عدد الطلاب']

    # Crear gráfico de barras
    fig = px.bar(
        status_counts,
        x='حالة السداد',
        y='عدد الطلاب',
        color='حالة السداد',
        color_discrete_map={
            'غير مسدد': '#FF5722',
            'مسدد جزئياً (أقل من 50%)': '#FFC107',
            'مسدد جزئياً (أكثر من 50%)': '#8BC34A',
            'مسدد بالكامل': '#4CAF50'
        },
        text='عدد الطلاب',
        title="توزيع الطلاب حسب حالة السداد"
    )

    # Personalizar diseño
    fig.update_layout(
        margin=dict(t=30, b=0, l=0, r=0),
        xaxis_title="",
        yaxis_title="عدد الطلاب",
        showlegend=False
    )

    fig.update_traces(textposition='outside')

    return fig
