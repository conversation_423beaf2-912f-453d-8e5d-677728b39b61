modules = ["python-3.11"]

[nix]
channel = "stable-24_05"

[deployment]
deploymentTarget = "autoscale"
run = ["sh", "-c", "bash -c \"chmod +x .pythonlibs/bin/python3.11 && .pythonlibs/bin/python3.11 -m ensurepip && .pythonlibs/bin/python3.11 -m pip install -r requirements.txt && .pythonlibs/bin/python3.11 setup_database.py --setup-db && .pythonlibs/bin/python3.11 -m streamlit run app.py --server.port 5000 --server.address 0.0.0.0\""]

[workflows]
runButton = "Project"

[[workflows.workflow]]
name = "Project"
mode = "parallel"
author = "agent"

[[workflows.workflow.tasks]]
task = "workflow.run"
args = "Streamlit App"

[[workflows.workflow]]
name = "Streamlit App"
author = "agent"

[workflows.workflow.metadata]
agentRequireRestartOnSave = false

[[workflows.workflow.tasks]]
task = "packager.installForAll"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "bash -c \"chmod +x .pythonlibs/bin/python3.11 && .pythonlibs/bin/python3.11 -m ensurepip && .pythonlibs/bin/python3.11 -m pip install -r requirements.txt && .pythonlibs/bin/python3.11 setup_database.py --setup-db && .pythonlibs/bin/python3.11 -m streamlit run app.py --server.port 5000 --server.address 0.0.0.0\""
waitForPort = 5000

[[ports]]
localPort = 5000
externalPort = 80
