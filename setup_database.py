#!/usr/bin/env python3
"""
سكريبت لإعداد قاعدة بيانات نظام إدارة ديون الطلاب
"""

import os
import sys
import time
import argparse
from sqlalchemy import create_engine
from sqlalchemy.exc import OperationalError

# التأكد من إمكانية استيراد الوحدات من الدليل الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def setup_database(db_url=None, retries=5, wait_time=2):
    """
    إعداد قاعدة البيانات وإنشاء الجداول
    
    Args:
        db_url (str, optional): رابط الاتصال بقاعدة البيانات
        retries (int): عدد محاولات الاتصال بقاعدة البيانات
        wait_time (int): وقت الانتظار بين المحاولات (بالثواني)
        
    Returns:
        bool: True إذا نجحت العملية، False خلاف ذلك
    """
    # إذا لم يتم تحديد رابط قاعدة البيانات، استخدم المتغير البيئي
    if not db_url:
        db_url = os.getenv('DATABASE_URL')
        # في حالة عدم وجود رابط قاعدة البيانات، استخدم قاعدة بيانات SQLite محلية
        if not db_url:
            print("تحذير: لم يتم تحديد رابط قاعدة البيانات DATABASE_URL في متغيرات البيئة")
            # إنشاء رابط لقاعدة بيانات SQLite محلية
            db_url = 'sqlite:///student_debts.db'
            print(f"سيتم استخدام قاعدة بيانات محلية: {db_url}")
            
    print(f"جاري الاتصال بقاعدة البيانات: {db_url.split('@')[-1]}")
    
    # محاولة الاتصال بقاعدة البيانات
    for attempt in range(retries):
        try:
            # إنشاء محرك قاعدة البيانات
            engine = create_engine(db_url)
            
            # اختبار الاتصال
            with engine.connect() as conn:
                print("تم الاتصال بقاعدة البيانات بنجاح.")
            
            # استيراد التعريفات من وحدة قاعدة البيانات
            from database import Base
            
            # إنشاء الجداول
            Base.metadata.create_all(engine)
            print("تم إنشاء جداول قاعدة البيانات بنجاح.")
            
            return True
        except OperationalError as e:
            print(f"محاولة {attempt + 1}/{retries} فشلت: {str(e)}")
            if attempt < retries - 1:
                print(f"الانتظار {wait_time} ثوانٍ قبل إعادة المحاولة...")
                time.sleep(wait_time)
            else:
                print("فشلت جميع محاولات الاتصال بقاعدة البيانات.")
                return False
        except Exception as e:
            print(f"حدث خطأ غير متوقع: {str(e)}")
            return False

def create_env_file(username, password, dbname, host="localhost", port=5432):
    """
    إنشاء ملف .env يحتوي على بيانات الاتصال بقاعدة البيانات
    
    Args:
        username (str): اسم المستخدم
        password (str): كلمة المرور
        dbname (str): اسم قاعدة البيانات
        host (str): المضيف
        port (int): المنفذ
        
    Returns:
        bool: True إذا نجحت العملية، False خلاف ذلك
    """
    try:
        # إنشاء رابط الاتصال بقاعدة البيانات
        db_url = f"postgresql://{username}:{password}@{host}:{port}/{dbname}"
        
        # كتابة ملف .env
        with open('.env', 'w') as f:
            f.write(f"DATABASE_URL={db_url}\n")
            f.write(f"PGUSER={username}\n")
            f.write(f"PGPASSWORD={password}\n")
            f.write(f"PGDATABASE={dbname}\n")
            f.write(f"PGHOST={host}\n")
            f.write(f"PGPORT={port}\n")
        
        print("تم إنشاء ملف .env بنجاح.")
        return True
    except Exception as e:
        print(f"حدث خطأ أثناء إنشاء ملف .env: {str(e)}")
        return False

def main():
    """
    الدالة الرئيسية للسكريبت
    """
    parser = argparse.ArgumentParser(description='إعداد قاعدة بيانات نظام إدارة ديون الطلاب')
    
    # إضافة المعلمات
    parser.add_argument('--create-env', action='store_true', help='إنشاء ملف .env باستخدام المعلمات المحددة')
    parser.add_argument('--db-url', help='رابط الاتصال بقاعدة البيانات')
    parser.add_argument('--username', help='اسم مستخدم قاعدة البيانات')
    parser.add_argument('--password', help='كلمة مرور قاعدة البيانات')
    parser.add_argument('--dbname', help='اسم قاعدة البيانات')
    parser.add_argument('--host', default='localhost', help='مضيف قاعدة البيانات')
    parser.add_argument('--port', type=int, default=5432, help='منفذ قاعدة البيانات')
    parser.add_argument('--setup-db', action='store_true', help='إعداد قاعدة البيانات وإنشاء الجداول')
    
    args = parser.parse_args()
    
    # التحقق من الإجراءات المطلوبة
    if not (args.create_env or args.setup_db):
        parser.print_help()
        return
    
    # إنشاء ملف .env إذا طلب المستخدم ذلك
    if args.create_env:
        if not all([args.username, args.password, args.dbname]):
            print("خطأ: يجب تحديد اسم المستخدم وكلمة المرور واسم قاعدة البيانات لإنشاء ملف .env.")
            return
        
        success = create_env_file(args.username, args.password, args.dbname, args.host, args.port)
        if not success:
            return
    
    # إعداد قاعدة البيانات إذا طلب المستخدم ذلك
    if args.setup_db:
        setup_database(args.db_url)
    
if __name__ == "__main__":
    main()