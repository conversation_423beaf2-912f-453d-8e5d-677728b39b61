OSError: cannot load library 'libpango-1.0-0': libpango-1.0-0: cannot open shared object file: No such file or directory. Additionally, ctypes.util.find_library() did not manage to locate a library called 'libpango-1.0-0'
Traceback:
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/streamlit/runtime/scriptrunner/exec_code.py", line 121, in exec_func_with_error_handling
    result = func()
             ^^^^^^
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/streamlit/runtime/scriptrunner/script_runner.py", line 640, in code_to_exec
    exec(code, module.__dict__)
File "/home/<USER>/workspace/app.py", line 10, in <module>
    from pdf_export import export_all_students_pdf, export_student_report_pdf
File "/home/<USER>/workspace/pdf_export.py", line 13, in <module>
    from weasyprint import HTML, CSS
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/weasyprint/__init__.py", line 430, in <module>
    from .css import preprocess_stylesheet  # noqa: I001, E402
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/weasyprint/css/__init__.py", line 28, in <module>
    from .computed_values import COMPUTER_FUNCTIONS
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/weasyprint/css/computed_values.py", line 9, in <module>
    from ..text.ffi import FROM_UNITS, ffi, pango
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/weasyprint/text/ffi.py", line 478, in <module>
    pango = _dlopen(
            ^^^^^^^^
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/weasyprint/text/ffi.py", line 463, in _dlopen
    return ffi.dlopen(names[0], flags)  # pragma: no cover
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/cffi/api.py", line 150, in dlopen
    lib, function_cache = _make_ffi_library(self, name, flags)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/cffi/api.py", line 834, in _make_ffi_library
    backendlib = _load_backend_lib(backend, libname, flags)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/cffi/api.py", line 829, in _load_backend_lib
    raise OSError(msg)
