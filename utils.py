import re
import pandas as pd
import openpyxl

def validate_student_name(name):
    """
    التحقق من صحة اسم الطالب
    
    Args:
        name (str): اسم الطالب للتحقق منه
        
    Returns:
        tuple: (is_valid, error_message)
    """
    # التحقق مما إذا كان الاسم فارغًا
    if not name or name.strip() == "":
        return False, "لا يمكن أن يكون اسم الطالب فارغًا."
    
    # التحقق مما إذا كان الاسم يحتوي على أحرف مسموح بها فقط
    # السماح بالأحرف العربية والإنجليزية والأرقام والمسافات وبعض الرموز
    if not re.match(r'^[\u0600-\u06FFa-zA-Z0-9\s\.\-\']+$', name):
        return False, "يحتوي اسم الطالب على أحرف غير صالحة."
    
    # التحقق مما إذا كان الاسم طويلاً جدًا
    if len(name) > 100:
        return False, "اسم الطالب طويل جدًا (الحد الأقصى 100 حرف)."
    
    return True, ""

def validate_debt_amount(amount):
    """
    التحقق من صحة مبلغ الدين
    
    Args:
        amount (str): مبلغ الدين للتحقق منه
        
    Returns:
        tuple: (is_valid, error_message)
    """
    # التحقق مما إذا كان المبلغ فارغًا
    if not amount or amount.strip() == "":
        return False, "لا يمكن أن يكون مبلغ الدين فارغًا."
    
    # إزالة أي رموز عملة وفواصل
    cleaned_amount = amount.replace('$', '').replace(',', '').strip()
    
    # التحقق مما إذا كان المبلغ رقمًا صالحًا
    try:
        value = float(cleaned_amount)
    except ValueError:
        return False, "يجب أن يكون مبلغ الدين رقمًا صالحًا."
    
    # التحقق مما إذا كان المبلغ موجبًا
    if value < 0:
        return False, "لا يمكن أن يكون مبلغ الدين سالبًا."
    
    # التحقق مما إذا كان المبلغ ليس كبيرًا جدًا
    if value > 1_000_000:
        return False, "مبلغ الدين كبير بشكل غير معقول."
    
    return True, ""

def format_currency(amount):
    """
    تنسيق رقم كعملة بالفلس الكويتي بدون فواصل - نسخة محسنة مع مزيد من التحقق من البيانات
    
    Args:
        amount (any): المبلغ المراد تنسيقه (بالفلس مباشرة) - يمكن أن يكون رقم أو نص أو None
        
    Returns:
        str: سلسلة العملة المنسقة بالفلس الكويتي بدون فواصل
    """
    # التعامل مع القيم الفارغة بشكل واضح
    if amount is None or amount == "" or amount == "None":
        return "0 فلس"
    
    # التعامل مع القيمة بناءً على نوعها
    try:
        # إذا كان المبلغ نصًا
        if isinstance(amount, str):
            # تنظيف النص من أي أحرف غير رقمية
            cleaned_amount = ''.join(c for c in amount if c.isdigit() or c == '.' or c == '-')
            if cleaned_amount:
                fils_amount = int(float(cleaned_amount))
            else:
                return "0 فلس"
        # إذا كان المبلغ رقمًا صحيحًا أو عشريًا
        elif isinstance(amount, (int, float)):
            fils_amount = int(amount)
        # إذا كان أي نوع آخر (مثل boolean أو غيره)
        else:
            # محاولة التحويل الآمن
            try:
                fils_amount = int(float(str(amount)))
            except:
                return "0 فلس"  # إذا فشل التحويل
    except (ValueError, TypeError, AttributeError):
        # إذا فشلت كل المحاولات
        return "0 فلس"
    
    # تنسيق المبلغ وإرجاعه كنص
    if fils_amount == 0:
        return "0 فلس"
    else:
        # ضمان أن الناتج دائمًا نص
        return str(fils_amount) + " فلس"

def generate_excel_template():
    """
    إنشاء قالب ملف Excel لاستيراد البيانات
    
    Returns:
        BytesIO: ملف Excel ككائن BytesIO
    """
    # إنشاء إطار بيانات قالب مع بيانات نموذجية وأوصاف في أول صف (المبالغ بالفلس)
    df = pd.DataFrame({
        'Student Name (اسم الطالب - مطلوب)': ['محمد أحمد', 'سارة علي', 'أحمد محمد'],
        'Debt Amount (مبلغ الدين بالفلس - مطلوب)': [100000, 250000, 500000],
        'Paid Amount (المبلغ المسدد بالفلس - اختياري)': [25000, 50000, 0]
    })
    
    # إنشاء كاتب Excel
    import io
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name='ديون الطلاب')
    
    output.seek(0)
    return output
