"""
وحدة متطورة لإنشاء تقارير PDF بتنسيق مطابق لوزارة التربية
تستخدم هذه الوحدة مكتبة FPDF مع تخصيص متقدم للتنسيق والمظهر
"""
import io
import base64
import pandas as pd
from datetime import datetime
from fpdf import FPDF


class SchoolReport(FPDF):
    """فئة متخصصة للتقارير المدرسية بنمط مشابه للنموذج الرسمي"""
    
    def __init__(self, orientation='P', unit='mm', format='A4'):
        super().__init__(orientation=orientation, unit=unit, format=format)
        self.add_font('DejaVu', '', 'DejaVuSansCondensed.ttf', uni=True)
        self.add_font('DejaVu', 'B', 'DejaVuSansCondensed.ttf', uni=True)
        self.add_font('DejaVu', 'I', 'DejaVuSansCondensed.ttf', uni=True)
        self.set_margins(15, 15, 15)
        self.add_page()
        self._set_title_and_header()
    
    def _set_title_and_header(self):
        """إضافة ترويسة رسمية للتقرير تشبه تقارير وزارة التربية"""
        # عنوان الصفحة واسم المدرسة
        self.set_font('DejaVu', 'B', 14)
        self.cell(0, 10, 'وزارة التربية', 0, 1, 'L')
        self.set_font('DejaVu', 'B', 12)
        self.cell(0, 7, 'نظام إدارة ديون الطلاب', 0, 1, 'R')
        
        # معلومات التقرير والعام الدراسي
        self.set_font('DejaVu', '', 10)
        self.cell(0, 7, f'العام الدراسي: {datetime.now().year}/{datetime.now().year+1}', 0, 1, 'R')
        
        # إضافة خط أفقي
        self.line(15, 40, 195, 40)
        self.ln(15)
    
    def add_report_title(self, title):
        """إضافة عنوان رئيسي للتقرير"""
        self.set_font('DejaVu', 'B', 16)
        # إطار معنون في الوسط
        self.set_fill_color(240, 240, 240)  # لون رمادي فاتح
        self.cell(0, 12, title, 0, 1, 'C', True)
        self.ln(5)
    
    def add_student_info(self, student_name):
        """إضافة معلومات الطالب في قسم منفصل"""
        self.set_font('DejaVu', 'B', 12)
        self.cell(0, 8, "معلومات الطالب", 0, 1, 'R')
        self.set_font('DejaVu', '', 11)
        
        # إطار معلومات الطالب
        self.set_fill_color(245, 245, 245)  # لون رمادي أفتح
        self.set_draw_color(200, 200, 200)  # لون حدود رمادي فاتح
        
        # اسم الطالب
        self.cell(120, 8, "", 0, 0, 'L')  # فراغ للمحاذاة
        self.cell(60, 8, f"اسم الطالب: {student_name}", 1, 1, 'R', True)
        self.ln(3)
    
    def add_summary_table(self, headers, data, header_height=10, row_height=8):
        """إضافة جدول ملخص البيانات بتنسيق أنيق"""
        self.set_font('DejaVu', 'B', 11)
        
        # حساب عروض الأعمدة - آخر عمود (اسم الطالب) أعرض
        col_count = len(headers)
        col_widths = [25, 30, 30, 30, 65] if col_count == 5 else [180 / col_count] * col_count
        
        # ألوان الجدول
        self.set_fill_color(230, 230, 230)  # رمادي للعناوين
        self.set_text_color(0, 0, 0)  # أسود للنص
        self.set_draw_color(120, 120, 120)  # رمادي متوسط للحدود
        self.set_line_width(0.3)  # سمك الخط
        
        # عناوين الجدول
        for i, header in enumerate(headers):
            # استخدام محاذاة مناسبة حسب نوع العمود
            align = 'R' if i == col_count - 1 else 'C'
            self.cell(col_widths[i], header_height, str(header), 1, 0, align, True)
        self.ln()
        
        # بيانات الجدول
        self.set_font('DejaVu', '', 10)
        
        # لون للتبادل بين الصفوف
        alt_fill = False
        
        for row in data:
            # التأكد من أن الصف يحتوي على عدد صحيح من الأعمدة
            if len(row) != col_count:
                row = row + [""] * (col_count - len(row)) if len(row) < col_count else row[:col_count]
            
            # تبديل لون الخلفية للصفوف المتناوبة
            alt_fill = not alt_fill
            if alt_fill:
                self.set_fill_color(245, 245, 245)  # رمادي فاتح جداً
            else:
                self.set_fill_color(255, 255, 255)  # أبيض
            
            # طباعة خلايا الصف
            for i, cell_data in enumerate(row):
                align = 'R' if i == col_count - 1 else 'C'
                self.cell(col_widths[i], row_height, str(cell_data) if cell_data is not None else "", 1, 0, align, True)
            self.ln()
    
    def add_debt_summary(self, debt_amount, paid_amount, remaining_amount, payment_status, payment_percentage):
        """إضافة ملخص حالة الدين في جدول منسق"""
        self.set_font('DejaVu', 'B', 12)
        self.cell(0, 10, "ملخص الدين", 0, 1, 'R')
        self.ln(2)
        
        # إنشاء جدول ملخص الدين
        self.set_fill_color(245, 245, 245)  # لون رمادي أفتح
        self.set_draw_color(180, 180, 180)  # لون حدود رمادي
        self.set_font('DejaVu', '', 10)
        
        # عناوين الأعمدة
        headers = ["نسبة السداد", "المبلغ المتبقي", "المبلغ المسدد", "مبلغ الدين"]
        col_widths = [40, 45, 45, 45]
        
        # العناوين
        self.set_font('DejaVu', 'B', 10)
        self.set_fill_color(230, 230, 230)  # رمادي للعناوين
        for i, header in enumerate(headers):
            self.cell(col_widths[i], 10, str(header), 1, 0, 'C', True)
        self.ln()
        
        # البيانات
        self.set_font('DejaVu', '', 10)
        self.set_fill_color(255, 255, 255)  # أبيض
        self.cell(col_widths[0], 8, f"{round(payment_percentage, 1)}%", 1, 0, 'C', True)
        self.cell(col_widths[1], 8, format_currency(remaining_amount), 1, 0, 'C', True)
        self.cell(col_widths[2], 8, format_currency(paid_amount), 1, 0, 'C', True)
        self.cell(col_widths[3], 8, format_currency(debt_amount), 1, 0, 'C', True)
        self.ln()
        
        # إضافة حالة السداد
        self.ln(5)
        self.set_font('DejaVu', 'B', 11)
        self.cell(40, 8, payment_status, 0, 0, 'C')
        self.cell(0, 8, ":حالة السداد", 0, 1, 'R')
    
    def add_transactions_table(self, transactions_data):
        """إضافة جدول المعاملات بتنسيق مشابه للنموذج"""
        self.add_page()  # بدء صفحة جديدة للمعاملات
        
        # عنوان
        self.set_font('DejaVu', 'B', 14)
        self.cell(0, 10, "سجل المعاملات", 0, 1, 'C')
        self.ln(5)
        
        if not transactions_data:
            self.set_font('DejaVu', '', 11)
            self.cell(0, 10, "لا توجد معاملات مسجلة", 0, 1, 'C')
            return
        
        # إنشاء جدول المعاملات
        headers = ["ملاحظات", "المبلغ", "نوع المعاملة", "التاريخ"]
        col_widths = [80, 30, 30, 40]
        
        self.set_fill_color(230, 230, 230)  # رمادي للعناوين
        self.set_draw_color(160, 160, 160)  # لون حدود رمادي متوسط
        
        # العناوين
        self.set_font('DejaVu', 'B', 11)
        for i, header in enumerate(headers):
            align = 'R' if i == 0 else 'C'
            self.cell(col_widths[i], 10, str(header), 1, 0, align, True)
        self.ln()
        
        # البيانات
        self.set_font('DejaVu', '', 10)
        alt_fill = False
        
        for row in transactions_data:
            # لون للصفوف المتناوبة
            alt_fill = not alt_fill
            fill_color = (245, 245, 245) if alt_fill else (255, 255, 255)
            self.set_fill_color(*fill_color)
            
            # طباعة خلايا الصف
            for i, cell_data in enumerate(row):
                align = 'R' if i == 0 else 'C'
                self.cell(col_widths[i], 8, str(cell_data) if cell_data is not None else "", 1, 0, align, True)
            self.ln()
    
    def add_footer(self):
        """إضافة تذييل للتقرير"""
        # الانتقال إلى أسفل الصفحة
        self.set_y(-25)
        self.set_font('DejaVu', 'I', 8)
        
        # خط أفقي
        self.line(15, self.get_y(), 195, self.get_y())
        self.ln(1)
        
        # نص التذييل
        current_date = datetime.now().strftime('%Y-%m-%d %H:%M')
        self.cell(95, 6, current_date, 0, 0, 'L')
        # تجنب عمليات الربط (+) مع النص
        footer_text = "نظام إدارة ديون الطلاب - المبالغ موضحة بالفلس الكويتي"
        self.cell(95, 6, footer_text, 0, 0, 'R')
        self.ln()


def format_currency(amount):
    """تنسيق المبلغ بأسلوب العملة"""
    try:
        # التأكد من أن المبلغ رقم
        if amount is None:
            return "0"
        
        # محاولة تحويل المبلغ إلى رقم إذا كان نصاً
        if isinstance(amount, str):
            try:
                amount = float(amount)
            except (ValueError, TypeError):
                return "0"
        
        # تقريب المبلغ وتنسيقه بدون كسور عشرية (الفلس هو الوحدة الأساسية)
        return f"{int(amount):,}"
    except:
        return "0"


def create_enhanced_student_report(student_name, debt_data, transactions_df):
    """
    إنشاء تقرير طالب بتنسيق محسن يشبه نماذج وزارة التربية
    
    Args:
        student_name: اسم الطالب
        debt_data: قاموس يحتوي على بيانات الدين
        transactions_df: إطار بيانات يحتوي على سجل المعاملات
        
    Returns:
        bytes: محتوى ملف PDF
    """
    try:
        # التحقق من البيانات
        if not student_name:
            student_name = "طالب غير معروف"
        
        if not debt_data:
            debt_data = {}
        
        # استخراج بيانات الدين
        try:
            debt_amount = float(debt_data.get('debt_amount', 0) or 0)
        except:
            debt_amount = 0
        
        try:
            paid_amount = float(debt_data.get('paid_amount', 0) or 0)
        except:
            paid_amount = 0
        
        # حساب القيم المشتقة
        remaining_amount = max(0, debt_amount - paid_amount)
        
        if debt_amount > 0:
            payment_percentage = (paid_amount / debt_amount) * 100
        else:
            payment_percentage = 0
        
        # تحديد حالة السداد
        if payment_percentage >= 100:
            payment_status = "مسدد بالكامل"
        elif payment_percentage > 0:
            payment_status = "مسدد جزئياً"
        else:
            payment_status = "غير مسدد"
        
        # إنشاء التقرير
        pdf = SchoolReport()
        
        # إضافة عنوان التقرير
        pdf.add_report_title("تقرير دين طالب")
        
        # إضافة معلومات الطالب
        pdf.add_student_info(student_name)
        
        # إضافة ملخص الدين
        pdf.add_debt_summary(
            debt_amount,
            paid_amount,
            remaining_amount,
            payment_status,
            payment_percentage
        )
        
        # إضافة سجل المعاملات
        transactions_data = []
        if transactions_df is not None and not transactions_df.empty:
            for _, row in transactions_df.iterrows():
                try:
                    # معالجة نوع المعاملة
                    transaction_type = 'دفع' if row.get('Transaction Type') == 'payment' else 'دين'
                    
                    # معالجة التاريخ
                    if isinstance(row.get('Transaction Date'), str):
                        date_str = row.get('Transaction Date', '')
                    else:
                        date_obj = row.get('Transaction Date')
                        if date_obj:
                            date_str = date_obj.strftime('%Y-%m-%d')
                        else:
                            date_str = ''
                    
                    # معالجة المبلغ والملاحظات
                    amount = row.get('Amount', 0)
                    notes = str(row.get('Notes', '')) if row.get('Notes') else ''
                    
                    # إضافة المعاملة
                    transactions_data.append([
                        notes,
                        format_currency(amount),
                        transaction_type,
                        date_str
                    ])
                except Exception as e:
                    print(f"خطأ في معالجة صف من المعاملات: {str(e)}")
        
        pdf.add_transactions_table(transactions_data)
        
        # إضافة تذييل
        pdf.add_footer()
        
        # إرجاع التقرير كبايتات
        try:
            return pdf.output(dest='S').encode('latin-1')
        except Exception as e:
            print(f"خطأ في إخراج PDF: {str(e)}")
            # محاولة أخرى بطريقة بديلة
            pdf = FPDF()
            pdf.add_page()
            return pdf.output(dest='S').encode('latin-1')
    
    except Exception as e:
        print(f"خطأ في إنشاء تقرير الطالب المحسن: {str(e)}")
        
        # إنشاء تقرير بسيط في حالة الخطأ
        pdf = FPDF()
        pdf.add_page()
        return pdf.output(dest='S').encode('latin-1')


def create_enhanced_all_students_report(df):
    """
    إنشاء تقرير محسن لجميع الطلاب بتنسيق مشابه للنموذج
    
    Args:
        df: إطار بيانات يحتوي على بيانات جميع الطلاب
        
    Returns:
        bytes: محتوى ملف PDF
    """
    print("بدء إنشاء تقرير جميع الطلاب")
    try:
        # التأكد من أن df ليس None
        if df is None:
            print("DataFrame فارغ: df هو None")
            df = pd.DataFrame()  # إنشاء إطار بيانات فارغ
            
        # التعامل مع جدول بيانات فارغ
        if df.empty:
            print("DataFrame فارغ: df.empty هو True")
            # إنشاء تقرير فارغ
            pdf = SchoolReport()
            pdf.add_report_title("تقرير جميع الطلاب")
            pdf.set_font('DejaVu', '', 12)
            pdf.cell(0, 10, "لا توجد بيانات طلاب متاحة", 0, 1, 'C')
            pdf.add_footer()
            return pdf.output(dest='S').encode('latin-1')
        
        # حساب المجاميع
        try:
            total_debt = df['Debt Amount'].fillna(0).astype(float).sum()
            total_paid = df['Paid Amount'].fillna(0).astype(float).sum()
            total_remaining = df['Remaining Amount'].fillna(0).astype(float).sum()
            student_count = len(df)
        except Exception as e:
            print(f"خطأ في حساب المجاميع: {str(e)}")
            total_debt = 0
            total_paid = 0
            total_remaining = 0
            student_count = 0
        
        # إنشاء التقرير
        pdf = SchoolReport()
        
        # إضافة عنوان التقرير
        pdf.add_report_title("تقرير ديون جميع الطلاب")
        
        # إضافة ملخص عام
        pdf.set_font('DejaVu', 'B', 12)
        pdf.cell(0, 10, "ملخص عام", 0, 1, 'R')
        pdf.set_font('DejaVu', '', 11)
        
        # ملخص في جدول
        pdf.set_fill_color(245, 245, 245)  # لون رمادي فاتح
        pdf.set_draw_color(180, 180, 180)
        
        # إجماليات
        headers = ["عدد الطلاب", "إجمالي المتبقي", "إجمالي المسدد", "إجمالي الديون"]
        data = [[
            str(student_count),
            format_currency(total_remaining),
            format_currency(total_paid),
            format_currency(total_debt)
        ]]
        
        # عرض جدول الإجماليات
        pdf.add_summary_table(headers, data)
        pdf.ln(10)
        
        # إضافة جدول تفاصيل الطلاب
        pdf.set_font('DejaVu', 'B', 12)
        pdf.cell(0, 10, "تفاصيل ديون الطلاب", 0, 1, 'R')
        
        # إعداد بيانات جدول الطلاب
        headers = ["نسبة السداد", "المبلغ المتبقي", "المبلغ المسدد", "مبلغ الدين", "اسم الطالب"]
        data = []
        
        for _, row in df.iterrows():
            try:
                student_name = str(row.get('Student Name', 'غير معروف'))
                
                try:
                    debt_amount = float(row.get('Debt Amount', 0) or 0)
                except:
                    debt_amount = 0
                
                try:
                    paid_amount = float(row.get('Paid Amount', 0) or 0)
                except:
                    paid_amount = 0
                
                try:
                    remaining_amount = float(row.get('Remaining Amount', 0) or 0)
                except:
                    remaining_amount = max(0, debt_amount - paid_amount)
                
                # حساب نسبة السداد
                if debt_amount > 0:
                    payment_percentage = (paid_amount / debt_amount) * 100
                else:
                    payment_percentage = 0
                
                # إضافة الصف إلى البيانات
                data.append([
                    f"{round(payment_percentage, 1)}%",
                    format_currency(remaining_amount),
                    format_currency(paid_amount),
                    format_currency(debt_amount),
                    student_name
                ])
            except Exception as e:
                print(f"خطأ في معالجة صف من البيانات: {str(e)}")
        
        # عرض جدول تفاصيل الطلاب
        pdf.add_summary_table(headers, data)
        
        # إضافة تذييل
        pdf.add_footer()
        
        # إرجاع التقرير كبايتات
        return pdf.output(dest='S').encode('latin-1')
    
    except Exception as e:
        print(f"خطأ في إنشاء تقرير جميع الطلاب المحسن: {str(e)}")
        
        # إنشاء تقرير بسيط في حالة الخطأ
        pdf = FPDF()
        pdf.add_page()
        return pdf.output(dest='S').encode('latin-1')


def create_enhanced_payment_status_report(fully_paid_df, partially_paid_df, unpaid_df):
    """
    إنشاء تقرير محسن لحالة السداد بتنسيق مشابه للنموذج
    
    Args:
        fully_paid_df: إطار بيانات الطلاب المسددين بالكامل
        partially_paid_df: إطار بيانات الطلاب المسددين جزئياً
        unpaid_df: إطار بيانات الطلاب غير المسددين
        
    Returns:
        bytes: محتوى ملف PDF
    """
    try:
        # التحقق من البيانات
        if fully_paid_df is None:
            fully_paid_df = pd.DataFrame()
        if partially_paid_df is None:
            partially_paid_df = pd.DataFrame()
        if unpaid_df is None:
            unpaid_df = pd.DataFrame()
        
        # حساب الإجماليات
        fully_paid_count = len(fully_paid_df)
        partially_paid_count = len(partially_paid_df)
        unpaid_count = len(unpaid_df)
        total_students = fully_paid_count + partially_paid_count + unpaid_count
        
        # إنشاء التقرير
        pdf = SchoolReport()
        
        # إضافة عنوان التقرير
        pdf.add_report_title("تقرير حالة السداد")
        
        # ملخص عام
        pdf.set_font('DejaVu', 'B', 12)
        pdf.cell(0, 10, "ملخص حالة السداد", 0, 1, 'R')
        
        # جدول ملخص أعداد الطلاب
        headers = ["المجموع", "غير المسددين", "المسددين جزئياً", "المسددين بالكامل"]
        data = [[
            str(total_students),
            str(unpaid_count),
            str(partially_paid_count),
            str(fully_paid_count)
        ]]
        
        pdf.add_summary_table(headers, data)
        pdf.ln(10)
        
        # تفاصيل الطلاب المسددين بالكامل
        if not fully_paid_df.empty:
            pdf.add_page()
            pdf.add_report_title("الطلاب المسددين بالكامل")
            
            headers = ["المبلغ المتبقي", "المبلغ المسدد", "مبلغ الدين", "اسم الطالب"]
            data = []
            
            for _, row in fully_paid_df.iterrows():
                data.append([
                    format_currency(row.get('Remaining Amount', 0)),
                    format_currency(row.get('Paid Amount', 0)),
                    format_currency(row.get('Debt Amount', 0)),
                    str(row.get('Student Name', ''))
                ])
            
            pdf.add_summary_table(headers, data)
        
        # تفاصيل الطلاب المسددين جزئياً
        if not partially_paid_df.empty:
            pdf.add_page()
            pdf.add_report_title("الطلاب المسددين جزئياً")
            
            headers = ["نسبة السداد", "المبلغ المتبقي", "المبلغ المسدد", "مبلغ الدين", "اسم الطالب"]
            data = []
            
            for _, row in partially_paid_df.iterrows():
                payment_percentage = row.get('Payment Percentage', 0)
                data.append([
                    f"{payment_percentage}%" if payment_percentage is not None else "0%",
                    format_currency(row.get('Remaining Amount', 0)),
                    format_currency(row.get('Paid Amount', 0)),
                    format_currency(row.get('Debt Amount', 0)),
                    str(row.get('Student Name', ''))
                ])
            
            pdf.add_summary_table(headers, data)
        
        # تفاصيل الطلاب غير المسددين
        if not unpaid_df.empty:
            pdf.add_page()
            pdf.add_report_title("الطلاب غير المسددين")
            
            headers = ["المبلغ المتبقي", "المبلغ المسدد", "مبلغ الدين", "اسم الطالب"]
            data = []
            
            for _, row in unpaid_df.iterrows():
                data.append([
                    format_currency(row.get('Remaining Amount', 0)),
                    format_currency(row.get('Paid Amount', 0)),
                    format_currency(row.get('Debt Amount', 0)),
                    str(row.get('Student Name', ''))
                ])
            
            pdf.add_summary_table(headers, data)
        
        # إضافة تذييل
        pdf.add_footer()
        
        # إرجاع التقرير كبايتات
        return pdf.output(dest='S').encode('latin-1')
    
    except Exception as e:
        print(f"خطأ في إنشاء تقرير حالة السداد المحسن: {str(e)}")
        
        # إنشاء تقرير بسيط في حالة الخطأ
        pdf = FPDF()
        pdf.add_page()
        return pdf.output(dest='S').encode('latin-1')


def create_download_link(pdf_bytes, filename):
    """إنشاء رابط تنزيل للملف"""
    try:
        if not pdf_bytes:
            return None
        
        b64 = base64.b64encode(pdf_bytes).decode()
        return f'<a href="data:application/pdf;base64,{b64}" download="{filename}" target="_blank">تنزيل التقرير</a>'
    except Exception as e:
        print(f"خطأ في إنشاء رابط التنزيل: {str(e)}")
        return None