#!/bin/bash
# سكريبت تثبيت نظام إدارة ديون الطلاب

# تعيين متغير لتتبع الأخطاء
ERRORS=0

# الألوان للإخراج
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # بدون لون

# دالة للطباعة مع لون
print_status() {
    echo -e "${YELLOW}$1${NC}"
}

print_success() {
    echo -e "${GREEN}$1${NC}"
}

print_error() {
    echo -e "${RED}$1${NC}"
    ERRORS=$((ERRORS+1))
}

# التحقق من وجود بايثون
print_status "جاري التحقق من تثبيت بايثون..."

# استخدام Python المثبت مباشرة
PYTHON_PATH=".pythonlibs/bin/python3.11"

if [ -f "$PYTHON_PATH" ]; then
    PYTHON_VERSION=$($PYTHON_PATH -c 'import sys; print(f"{sys.version_info.major}.{sys.version_info.minor}")')
    print_success "تم العثور على بايثون $PYTHON_VERSION في $PYTHON_PATH"
else
    print_error "لم يتم العثور على بايثون في $PYTHON_PATH. يرجى التحقق من تثبيت بايثون."
    exit 1
fi

# التحقق من تثبيت pip
print_status "جاري التحقق من تثبيت pip..."
# استخدام pip من المسار المحدد
PIP_PATH="$PYTHON_PATH -m pip"

# التحقق من pip
$PIP_PATH --version > /dev/null 2>&1
if [ $? -eq 0 ]; then
    print_success "تم العثور على pip"
else
    print_error "لم يتم العثور على pip. جاري محاولة تثبيته..."
    $PYTHON_PATH -m ensurepip
fi

# تثبيت المكتبات المطلوبة
print_status "جاري تثبيت المكتبات المطلوبة..."
$PIP_PATH install streamlit pandas plotly sqlalchemy psycopg2-binary openpyxl fpdf

if [ $? -ne 0 ]; then
    print_error "فشل تثبيت المكتبات المطلوبة."
    exit 1
else
    print_success "تم تثبيت المكتبات المطلوبة بنجاح."
fi

# استخدام قاعدة البيانات PostgreSQL المتوفرة في بيئة Replit
print_status "استخدام قاعدة البيانات PostgreSQL المتوفرة في بيئة Replit..."

# إعداد قاعدة البيانات
print_status "جاري إعداد قاعدة البيانات..."
$PYTHON_PATH setup_database.py --setup-db

if [ $? -ne 0 ]; then
    print_error "فشل إعداد قاعدة البيانات."
    exit 1
else
    print_success "تم إعداد قاعدة البيانات بنجاح."
fi

# إذا وصلنا إلى هنا، فقد تم التثبيت بنجاح
if [ $ERRORS -eq 0 ]; then
    print_success "===================================="
    print_success "تم تثبيت النظام بنجاح!"
    print_success "سيتم تشغيل النظام على المنفذ 5000:"
    print_success "$PYTHON_PATH -m streamlit run app.py --server.port 5000 --server.address 0.0.0.0"
    print_success "===================================="
else
    print_error "حدثت أخطاء أثناء التثبيت. يرجى مراجعة الأخطاء أعلاه."
fi