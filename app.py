import streamlit as st
import pandas as pd
import datetime
import io
import plotly.express as px
from database import DatabaseManager
from utils import validate_student_name, validate_debt_amount, format_currency, generate_excel_template

# Importar módulos de la nueva interfaz
from ui_theme import apply_theme
from ui_components import card, metric_card, status_badge, info_box
from dashboard import show_dashboard, create_enhanced_debt_visualization, create_enhanced_payment_status_chart

def create_print_button(label="طباعة الجدول", key=None):
    """
    إنشاء زر طباعة يطبع الجدول المعروض فقط

    Args:
        label (str): عنوان زر الطباعة
        key (str): مفتاح فريد للزر

    Returns:
        None: ينتج زر Streamlit بوظيفة JavaScript للطباعة
    """
    # إنشاء معرف فريد للزر
    if key is None:
        import re
        clean_label = re.sub(r'[^a-zA-Z0-9]', '_', label)
        btn_id = f"print_btn_{clean_label}_{int(datetime.datetime.now().timestamp())}"
    else:
        btn_id = f"print_btn_{key}_{int(datetime.datetime.now().timestamp())}"

    # إنشاء HTML مباشر للزر
    html_button = f"""
    <div style="margin: 10px 0;">
        <button id="{btn_id}" style="
            background-color: #009688;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 16px;
            font-size: 14px;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        ">
            <span style="margin-right: 8px;">🖨️</span> {label}
        </button>
    </div>

    <script>
        (function() {{
            // التأكد من وجود الزر في الصفحة
            const checkExist = setInterval(function() {{
                const printBtn = document.getElementById('{btn_id}');
                if (printBtn) {{
                    clearInterval(checkExist);

                    // إضافة حدث النقر
                    printBtn.addEventListener('click', function() {{
                        // جمع جميع جداول البيانات المعروضة حاليًا
                        const tables = document.querySelectorAll('.stDataFrame table');

                        // إذا لم توجد جداول، عرض رسالة تنبيه
                        if (!tables || tables.length === 0) {{
                            alert('لا توجد بيانات للطباعة');
                            return;
                        }}

                        // إنشاء نافذة جديدة للطباعة
                        const printWindow = window.open('', '_blank');

                        // التحقق من فتح النافذة بنجاح
                        if (!printWindow) {{
                            alert('يرجى السماح بالنوافذ المنبثقة في متصفحك لاستخدام ميزة الطباعة');
                            return;
                        }}

                        // إنشاء محتوى نافذة الطباعة
                        let printContent = `
                            <!DOCTYPE html>
                            <html dir="rtl">
                            <head>
                                <meta charset="UTF-8">
                                <title>طباعة بيانات ديون الطلاب</title>
                                <style>
                                    body {{
                                        font-family: Arial, sans-serif;
                                        direction: rtl;
                                        padding: 20px;
                                        margin: 0;
                                    }}

                                    h1 {{
                                        color: #1976D2;
                                        text-align: center;
                                        margin-bottom: 5px;
                                        font-size: 22px;
                                    }}

                                    h2 {{
                                        color: #1E6496;
                                        text-align: center;
                                        margin-bottom: 20px;
                                        font-size: 18px;
                                    }}

                                    .print-date {{
                                        text-align: left;
                                        margin-bottom: 20px;
                                        font-size: 12px;
                                        color: #666;
                                    }}

                                    table {{
                                        width: 100%;
                                        border-collapse: collapse;
                                        margin-bottom: 30px;
                                    }}

                                    th {{
                                        background-color: #f2f2f2;
                                        padding: 12px;
                                        text-align: right;
                                        border: 1px solid #ddd;
                                        font-weight: bold;
                                    }}

                                    td {{
                                        padding: 10px;
                                        text-align: right;
                                        border: 1px solid #ddd;
                                    }}

                                    tr:nth-child(even) {{
                                        background-color: #f9f9f9;
                                    }}

                                    @media print {{
                                        body {{
                                            padding: 0;
                                            margin: 0;
                                        }}

                                        button {{
                                            display: none;
                                        }}
                                    }}
                                </style>
                            </head>
                            <body>
                                <h1>نظام إدارة ديون الطلاب</h1>
                                <h2>{label}</h2>
                                <div class="print-date">تاريخ الطباعة: ${{new Date().toLocaleDateString('ar-EG')}}</div>
                        `;

                        // نسخ محتوى الجداول
                        for (let i = 0; i < tables.length; i++) {{
                            printContent += '<div class="table-container">' + tables[i].outerHTML + '</div>';
                        }}

                        // إكمال المحتوى
                        printContent += `
                                <div style="text-align: center; margin-top: 30px; font-size: 12px; color: #666;">
                                    <p>تم إصدار هذا التقرير بواسطة نظام إدارة ديون الطلاب</p>
                                </div>
                            </body>
                            </html>
                        `;

                        // كتابة المحتوى في النافذة الجديدة
                        printWindow.document.open();
                        printWindow.document.write(printContent);
                        printWindow.document.close();

                        // طباعة المحتوى بعد تحميل الصفحة بشكل كامل
                        printWindow.onload = function() {{
                            // استخدم تأخير قصير لضمان تحميل الأنماط والصور
                            setTimeout(function() {{
                                printWindow.print();

                                // إغلاق النافذة بعد الطباعة (أو إلغائها)
                                printWindow.onafterprint = function() {{
                                    printWindow.close();
                                }};
                            }}, 500);
                        }};
                    }});

                    // إضافة تأثير عند تحويم المؤشر فوق الزر
                    printBtn.addEventListener('mouseover', function() {{
                        this.style.backgroundColor = '#00796b';
                        this.style.transform = 'translateY(-2px)';
                        this.style.boxShadow = '0 4px 8px rgba(0,0,0,0.2)';
                        this.style.transition = 'all 0.3s ease';
                    }});

                    printBtn.addEventListener('mouseout', function() {{
                        this.style.backgroundColor = '#009688';
                        this.style.transform = 'translateY(0)';
                        this.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';
                        this.style.transition = 'all 0.3s ease';
                    }});
                }}
            }}, 100);
        }})();
    </script>
    """

    # إنشاء عنصر HTML في التطبيق
    st.markdown(html_button, unsafe_allow_html=True)
# استيراد وظائف التقرير
# استيراد وظائف التقرير مباشرة من simple_report
from simple_report import create_all_students_report
from simple_report import create_student_report
from simple_report import create_payment_status_report

# إعدادات الصفحة
st.set_page_config(
    page_title="نظام إدارة ديون الطلاب",
    page_icon="📚",
    layout="wide"
)

# تهيئة متغير حالة للسمة
if 'theme' not in st.session_state:
    st.session_state.theme = 'فاتح'

# تطبيق السمة المحددة
theme_css = apply_theme(st.session_state.theme)
st.markdown(f"<style>{theme_css}</style>", unsafe_allow_html=True)

# تهيئة مدير قاعدة البيانات
if 'debt_manager' not in st.session_state:
    st.session_state.debt_manager = DatabaseManager()

# تهيئة متغير حالة لتخزين المعلومات المحددة
if 'selected_student_info' not in st.session_state:
    st.session_state.selected_student_info = {}

# تهيئة متغير حالة لمعالجة إدخال البيانات بمجرد الضغط على Enter
if 'process_on_next_render' not in st.session_state:
    st.session_state.process_on_next_render = False

# العنوان الرئيسي
st.title("نظام إدارة ديون الطلاب")

# الشريط الجانبي للتنقل
st.sidebar.title("نظام إدارة ديون الطلاب")

# إضافة اختيار السمة
theme_selection = st.sidebar.selectbox(
    "المظهر",
    ["فاتح", "داكن"],
    index=0 if st.session_state.theme == 'فاتح' else 1
)

# تحديث السمة إذا تغيرت
if theme_selection != st.session_state.theme:
    st.session_state.theme = theme_selection
    st.rerun()

st.sidebar.markdown("---")
st.sidebar.markdown("### القائمة الرئيسية")

# صفحات التنقل الرئيسية مع أيقونات
pages = {
    "لوحة المعلومات": "📊",
    "إضافة/تحديث الديون": "✏️",
    "عرض جميع الديون": "👥",
    "التقارير": "📝",
    "الفواتير": "🧾",
    "استيراد/تصدير": "💾",
}

page = st.sidebar.radio(
    "اختر الصفحة",
    list(pages.keys()),
    format_func=lambda x: f"{pages[x]} {x}"
)

# التحقق مما إذا كان يجب معالجة البيانات (إذا تم الضغط على Enter في الشاشة السابقة)
if st.session_state.process_on_next_render:
    # إعادة تعيين متغير الحالة لمنع المعالجة المتكررة
    st.session_state.process_on_next_render = False

    # تنفيذ معالجة النموذج فقط إذا كنا في صفحة إضافة/تحديث الديون
    if page == "إضافة/تحديث الديون":
        # التحقق من وجود البيانات المطلوبة
        if 'student_name' in st.session_state and 'debt_amount' in st.session_state:
            try:
                student_name = st.session_state.student_name
                debt_amount = st.session_state.debt_amount
                paid_amount = st.session_state.paid_amount if 'paid_amount' in st.session_state else "0"

                # التحقق من صحة البيانات
                name_valid, name_error = validate_student_name(student_name)
                amount_valid, amount_error = validate_debt_amount(debt_amount)
                paid_valid, paid_error = validate_debt_amount(paid_amount) if paid_amount else (True, "")

                if name_valid and amount_valid and paid_valid:
                    amount = float(debt_amount)
                    paid = float(paid_amount) if paid_amount else 0.0

                    # التحقق مما إذا كان الطالب موجودًا بالفعل
                    if student_name in st.session_state.debt_manager.get_all_students():
                        # تحديد طريقة التحديث (للطلاب الموجودين)
                        is_debt_additional = ('debt_operation_type' in st.session_state and
                                            st.session_state.debt_operation_type == "إضافة مبلغ للدين الحالي")
                        is_paid_additional = ('paid_operation_type' in st.session_state and
                                             st.session_state.paid_operation_type == "إضافة مبلغ للمسدد الحالي")

                        # إضافة ملاحظات للمعاملات
                        debt_notes = f"تحديث يدوي - Enter - {datetime.datetime.now().strftime('%Y-%m-%d')}"
                        payment_notes = f"تحديث يدوي - Enter - {datetime.datetime.now().strftime('%Y-%m-%d')}"

                        # تحديث الدين والمبلغ المسدد
                        debt_success = st.session_state.debt_manager.update_debt(
                            student_name, amount, is_additional=is_debt_additional, notes=debt_notes)
                        paid_success = st.session_state.debt_manager.update_paid_amount(
                            student_name, paid, is_additional=is_paid_additional, notes=payment_notes)

                    else:
                        # إضافة طالب جديد
                        add_notes = f"إضافة طالب جديد - Enter - {datetime.datetime.now().strftime('%Y-%m-%d')}"
                        payment_notes = f"تسديد أولي - Enter - {datetime.datetime.now().strftime('%Y-%m-%d')}"

                        if st.session_state.debt_manager.add_debt(student_name, amount, notes=add_notes):
                            if paid > 0:
                                st.session_state.debt_manager.update_paid_amount(student_name, paid, notes=payment_notes)

                    # إعادة تعيين النموذج
                    st.session_state.student_name = ""
                    st.session_state.debt_amount = ""
                    st.session_state.paid_amount = "0"
                    # نعيد تمكين الإدخال بعد نجاح العملية
                    st.session_state.enable_enter_key = True

            except Exception as e:
                st.error(f"حدث خطأ أثناء معالجة النموذج: {str(e)}")

# عرض الصفحة المناسبة بناءً على الاختيار
if page == "لوحة المعلومات":
    # عرض لوحة المعلومات الرئيسية
    show_dashboard(st.session_state.debt_manager)
else:
    # إضافة ملخص لإجمالي المديونيات في أعلى الصفحات الأخرى
    if not st.session_state.debt_manager.is_empty():
        # الحصول على جميع سجلات الديون
        df = st.session_state.debt_manager.get_dataframe()

        # عرض إجمالي الدين والمبالغ المسددة والمتبقية
        total_debt = df['Debt Amount'].sum()
        total_paid = df['Paid Amount'].sum()
        total_remaining = df['Remaining Amount'].sum()

        # عرض الإجماليات في أعلى الصفحة باستخدام المكونات الجديدة
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            metric_card("إجمالي عدد الطلاب", f"{len(df)}")
        with col2:
            metric_card("إجمالي الديون", format_currency(total_debt))
        with col3:
            metric_card("إجمالي المسدد", format_currency(total_paid))
        with col4:
            metric_card("إجمالي المتبقي", format_currency(total_remaining))

        # إضافة خط فاصل
        st.markdown("---")

# إضافة قائمة الطلاب للوصول السريع
with st.sidebar:
    # إضافة قائمة بأسماء الطلاب للتنقل السريع إذا كانت هناك سجلات
    if not st.session_state.debt_manager.is_empty():
        st.markdown("---")
        st.markdown("### الطلاب:")

        # الحصول على قائمة أسماء الطلاب
        student_names = st.session_state.debt_manager.get_all_students()

        # تخزين الطالب المحدد في حالة الجلسة
        if 'selected_student_from_sidebar' not in st.session_state:
            st.session_state['selected_student_from_sidebar'] = None

        # عرض قائمة الطلاب كأزرار
        for name in student_names:
            if st.button(name, key=f"sidebar_student_{name}"):
                # تخزين اسم الطالب المحدد
                st.session_state['selected_student_from_sidebar'] = name
                # التأكد من أن الصفحة النشطة هي صفحة الإضافة/التحديث
                if page != "إضافة/تحديث الديون":
                    page = "إضافة/تحديث الديون"
                # الانتقال إلى صفحة الطالب مع تعيين الحالة المطلوبة
                st.session_state["selected_student_info"] = {
                    "name": name,
                    "debt_amount": st.session_state.debt_manager.get_debt(name),
                    "paid_amount": st.session_state.debt_manager.get_paid_amount(name) or 0
                }
                st.rerun()

# إضافة خيار للتنزيل السريع لملف Excel الفارغ
with st.sidebar:
    st.markdown("---")
    st.markdown("### تنزيل سريع:")

    # زر لتحميل قالب فارغ
    if st.button("تحميل قالب Excel فارغ", key="sidebar_download"):
        # إنشاء قالب فارغ
        excel_template = generate_excel_template()
        # إنشاء رابط تنزيل
        st.download_button(
            label="تنزيل القالب الآن",
            data=excel_template,
            file_name="نظام_إدارة_ديون_الطلاب.xlsx",
            mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            key="sidebar_download_button"
        )

# وظيفة لإعادة تعيين قيم النموذج ومتغيرات الحالة
def reset_form():
    # تنظيف جميع متغيرات الحالة ذات الصلة
    if "selected_student_info" in st.session_state:
        st.session_state["selected_student_info"] = {}

    if "selected_student_from_sidebar" in st.session_state:
        st.session_state['selected_student_from_sidebar'] = None

    if "similar_names" in st.session_state:
        st.session_state.similar_names = []

    if "search_results" in st.session_state:
        st.session_state.search_results = False

if page == "إضافة/تحديث الديون":
    st.header("إضافة أو تحديث دين طالب")

    # تهيئة متغيرات للبحث عن الاسم
    if 'similar_names' not in st.session_state:
        st.session_state.similar_names = []

    # إعداد متغير لتخزين نتيجة البحث
    if 'search_results' not in st.session_state:
        st.session_state.search_results = False

    # حقول الإدخال خارج النموذج للبحث الفوري
    st.markdown("### 🔍 بحث عن اسم الطالب")

    col1, col2 = st.columns([3, 1])

    with col1:
        student_name_search = st.text_input("ابدأ بكتابة اسم الطالب", key="student_name_search")

    with col2:
        st.write("&nbsp;")  # مسافة فارغة للمحاذاة
        search_button = st.button("🔍 بحث", type="primary")

    # البحث عن الأسماء المشابهة
    if (student_name_search and len(student_name_search) >= 2) or search_button:
        # عند البحث، حفظ النتائج في متغير حالة منفصل
        search_results = st.session_state.debt_manager.find_similar_names(student_name_search)
        if search_results != st.session_state.similar_names:
            st.session_state.similar_names = search_results
            st.session_state.search_results = True

    # عرض نتائج البحث والسماح باختيار طالب
    selected_student = None
    if st.session_state.similar_names:
        st.markdown("### 🔎 نتائج البحث")
        st.markdown("الطلاب المطابقين للبحث:")

        # عرض الأسماء المشابهة كأزرار في صفوف
        cols = st.columns(3)
        for i, name in enumerate(st.session_state.similar_names):
            button_key = f"select_student_{name}"
            with cols[i % 3]:
                if st.button(f"اختر: {name}", key=button_key, use_container_width=True):
                    # تخزين معلومات الطالب في حالة الجلسة
                    st.session_state["selected_student_info"] = {
                        "name": name,
                        "debt_amount": st.session_state.debt_manager.get_debt(name),
                        "paid_amount": st.session_state.debt_manager.get_paid_amount(name) or 0
                    }
                    st.rerun()

    # عرض معلومات الطالب المحدد إذا كانت موجودة
    if "selected_student_info" in st.session_state and st.session_state["selected_student_info"] and "name" in st.session_state["selected_student_info"]:
        info = st.session_state["selected_student_info"]

        # بطاقة معلومات الطالب
        st.markdown(f"### 👤 معلومات الطالب: {info['name']}")

        # احتساب نسبة السداد
        payment_percentage = 0
        if info['debt_amount'] > 0:
            payment_percentage = (info['paid_amount'] / info['debt_amount']) * 100

        # تحديد حالة السداد
        payment_status = "غير مسدد"
        status_type = "error"
        if payment_percentage >= 100:
            payment_status = "مسدد بالكامل"
            status_type = "success"
        elif payment_percentage > 0:
            payment_status = "مسدد جزئياً"
            status_type = "warning"

        # عرض المعلومات الأساسية في صفوف
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            metric_card("قيمة الدين", format_currency(info['debt_amount']))
        with col2:
            metric_card("المبلغ المسدد", format_currency(info['paid_amount']))
        with col3:
            metric_card("المتبقي", format_currency(info['debt_amount'] - info['paid_amount']))
        with col4:
            metric_card("نسبة السداد", f"{payment_percentage:.1f}", suffix="%")

        # عرض حالة السداد باستخدام مكون البطاقة
        st.markdown(f"""
        <div style="text-align: center; margin: 20px 0;">
            {status_badge(f"حالة السداد: {payment_status}", status_type)}
        </div>
        """, unsafe_allow_html=True)

        # عرض سجل المعاملات للطالب
        st.markdown(f"### 📋 سجل معاملات الطالب: {info['name']}")

        transactions_df = st.session_state.debt_manager.get_student_transactions(info['name'])

        if not transactions_df.empty:
            # تنسيق العملة بالفلس مباشرة
            transactions_df['Formatted Amount'] = transactions_df['Amount'].apply(lambda x: format_currency(x))

            # تنسيق التاريخ في عمود التاريخ
            transactions_df['Transaction Date'] = transactions_df['Transaction Date'].apply(
                lambda x: x.strftime("%Y-%m-%d %H:%M:%S")
            )

            # إعداد العمود الذي سيتم عرضه (استبدال عمود المبلغ الأصلي بالمبلغ المنسق)
            display_df = transactions_df[['Transaction Type', 'Formatted Amount', 'Transaction Date', 'Notes']].rename(
                columns={
                    'Formatted Amount': 'المبلغ',
                    'Transaction Type': 'نوع المعاملة',
                    'Transaction Date': 'تاريخ المعاملة',
                    'Notes': 'ملاحظات'
                }
            )

            # عرض جدول المعاملات
            st.dataframe(display_df, use_container_width=True)
        else:
            info_box("لا توجد معاملات مسجلة لهذا الطالب.", "info")

        # أزرار التحكم
        st.markdown("### إجراءات")
        col1, col2, col3 = st.columns(3)

        # زر إلغاء الاختيار
        if col1.button("🔙 إلغاء اختيار الطالب"):
            del st.session_state["selected_student_info"]
            st.rerun()

        # زر حذف الطالب
        if col2.button("🗑️ حذف الطالب", type="primary", help="حذف هذا الطالب وجميع بياناته"):
            if st.session_state.debt_manager.delete_debt(info['name']):
                st.success(f"تم حذف الطالب {info['name']} بنجاح")
                del st.session_state["selected_student_info"]
                st.rerun()
            else:
                st.error(f"فشل حذف الطالب {info['name']}")

        # زر تصدير تقرير PDF للطالب
        if col3.button("📄 تصدير PDF", help="تصدير تقرير مفصل للطالب بصيغة PDF"):
            try:
                # إنشاء ملف PDF باستخدام المكتبة الجديدة
                pdf_bytes = create_student_report(
                    info['name'],
                    {
                        'debt_amount': info['debt_amount'],
                        'paid_amount': info['paid_amount'],
                    },
                    transactions_df
                )

                # التحقق من وجود الملف قبل عرض زر التنزيل
                if pdf_bytes:
                    # توفير رابط التنزيل
                    st.download_button(
                        label="تنزيل تقرير الطالب PDF",
                        data=pdf_bytes,
                        file_name=f"student_report_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
                        mime="application/pdf"
                    )
                    st.success("تم إنشاء تقرير PDF بنجاح!")
                else:
                    # لن يحدث هذا لأن الدالة محسنة لإرجاع بيانات صالحة دائمًا
                    st.error("فشل إنشاء ملف PDF. لا توجد بيانات مرجعة.")
            except Exception as e:
                st.error(f"فشل إنشاء تقرير PDF: {str(e)}")
                # إضافة مزيد من المعلومات للتصحيح
                st.error(f"نوع الخطأ: {type(e).__name__}")

        # إضافة زر تسديد المديونية بالكامل
        col1, col2 = st.columns(2)
        remaining = info['debt_amount'] - info['paid_amount']
        if remaining > 0:
            # عرض المبلغ المتبقي وزر التسديد الكامل
            if col1.button(f"💰 تسديد المديونية بالكامل ({format_currency(remaining)})", type="primary"):
                payment_notes = f"تسديد كامل المديونية - {datetime.datetime.now().strftime('%Y-%m-%d')}"
                if st.session_state.debt_manager.update_paid_amount(
                    info['name'],
                    info['debt_amount'],  # تعيين المبلغ المدفوع ليكون مساوياً للدين
                    is_additional=False,  # استبدال القيمة الحالية
                    notes=payment_notes
                ):
                    st.success(f"تم تسديد كامل مديونية الطالب {info['name']} بنجاح!")
                    # تحديث معلومات الطالب في حالة الجلسة
                    st.session_state["selected_student_info"]["paid_amount"] = info['debt_amount']
                    st.rerun()
                else:
                    st.error(f"حدث خطأ أثناء تسديد المديونية للطالب {info['name']}")
        else:
            col1.success(f"تمت تسديد كامل المديونية للطالب {info['name']}")
            col2.info("لا توجد مبالغ متبقية للتسديد")

    # نموذج إضافة/تحديث الدين
    st.markdown("### 💾 نموذج إضافة/تحديث الدين")

    # تهيئة متغير حالة لمعالجة الضغط على Enter
    if 'enable_enter_key' not in st.session_state:
        st.session_state.enable_enter_key = True

    # دالة للمعالجة عند الضغط على Enter
    def handle_enter_key():
        if st.session_state.enable_enter_key:
            # تعطيل مؤقتاً لمنع التنفيذ المتكرر
            st.session_state.enable_enter_key = False
            # تعيين متغير حالة لتنفيذ العملية في المرة القادمة عند إعادة تشغيل التطبيق
            st.session_state.process_on_next_render = True

    # تحديد القيم الأولية للحقول
    initial_name = ""
    initial_debt = ""
    initial_paid = "0"
    is_existing_student = False

    # استخدام معلومات الطالب المحدد إذا كانت موجودة
    if "selected_student_info" in st.session_state and st.session_state["selected_student_info"] and "name" in st.session_state["selected_student_info"]:
        info = st.session_state["selected_student_info"]
        initial_name = info["name"]
        initial_debt = str(info["debt_amount"])
        initial_paid = str(info["paid_amount"])
        is_existing_student = True

    # تنبيه بحالة التحديث
    if is_existing_student:
        st.info(f"أنت تقوم بتحديث بيانات الطالب: {initial_name}")

    # دالة معالجة البيانات عند الضغط على Enter أو الحفظ
    def process_form_data():
        # التحقق من صحة بيانات الإدخال
        student_name = st.session_state.student_name
        debt_amount = st.session_state.debt_amount
        paid_amount = st.session_state.paid_amount

        # تحديد طريقة التحديث للطلاب الموجودين
        if is_existing_student:
            debt_operation = st.session_state.debt_operation_type
            paid_operation = st.session_state.paid_operation_type
        else:
            debt_operation = "تعيين قيمة جديدة"
            paid_operation = "تعيين قيمة جديدة"

        name_valid, name_error = validate_student_name(student_name)
        amount_valid, amount_error = validate_debt_amount(debt_amount)
        paid_valid, paid_error = validate_debt_amount(paid_amount) if paid_amount else (True, "")

        if not name_valid:
            st.error(name_error)
            return False
        elif not amount_valid:
            st.error(amount_error)
            return False
        elif not paid_valid:
            st.error(paid_error)
            return False

        # إضافة أو تحديث سجل الدين
        try:
            amount = float(debt_amount)
            paid = float(paid_amount) if paid_amount else 0.0

            # التحقق مما إذا كان الطالب موجودًا بالفعل
            if student_name in st.session_state.debt_manager.get_all_students():
                # تحديد طريقة التحديث
                is_debt_additional = (debt_operation == "إضافة مبلغ للدين الحالي")
                is_paid_additional = (paid_operation == "إضافة مبلغ للمسدد الحالي")

                # إضافة ملاحظات للمعاملات
                debt_notes = f"تحديث يدوي من النظام - {datetime.datetime.now().strftime('%Y-%m-%d')}"
                payment_notes = f"تحديث يدوي من النظام - {datetime.datetime.now().strftime('%Y-%m-%d')}"

                # محاولة تحديث الدين
                debt_success = st.session_state.debt_manager.update_debt(
                    student_name,
                    amount,
                    is_additional=is_debt_additional,
                    notes=debt_notes
                )

                if not debt_success:
                    st.error(f"فشل تحديث دين {student_name}")

                # محاولة تحديث المبلغ المسدد
                paid_success = st.session_state.debt_manager.update_paid_amount(
                    student_name,
                    paid,
                    is_additional=is_paid_additional,
                    notes=payment_notes
                )

                if not paid_success:
                    st.error(f"فشل تحديث المبلغ المسدد لـ {student_name}")

                # إذا نجحت عملية واحدة على الأقل، نعرض رسالة نجاح
                if debt_success or paid_success:
                    if debt_success and paid_success:
                        st.success(f"تم تحديث بيانات {student_name} بنجاح!")
                    elif debt_success:
                        st.success(f"تم تحديث دين {student_name} بنجاح!")
                    elif paid_success:
                        st.success(f"تم تحديث المبلغ المسدد لـ {student_name} بنجاح!")
            else:
                # إضافة طالب جديد
                add_notes = f"إضافة طالب جديد - {datetime.datetime.now().strftime('%Y-%m-%d')}"
                payment_notes = f"تسديد أولي - {datetime.datetime.now().strftime('%Y-%m-%d')}"

                if st.session_state.debt_manager.add_debt(student_name, amount, notes=add_notes):
                    if paid > 0:
                        st.session_state.debt_manager.update_paid_amount(student_name, paid, notes=payment_notes)
                    st.success(f"تمت إضافة {student_name} بنجاح!")
                else:
                    st.error(f"فشلت إضافة {student_name}")

            # إعادة تعيين النموذج وقائمة البحث
            reset_form()
            return True
        except Exception as e:
            st.error(f"خطأ: {str(e)}")
            return False

    # نموذج الإدخال بدون التفاف Form
    with st.container():
        col1, col2 = st.columns([3, 1])

        with col1:
            st.text_input("اسم الطالب",
                        value=initial_name,
                        key="student_name",
                        on_change=handle_enter_key,
                        placeholder="أدخل اسم الطالب...")

        with col2:
            st.markdown("&nbsp;")  # مسافة فارغة للمحاذاة
            if is_existing_student:
                debt_operation_options = [
                    "تعيين قيمة جديدة",
                    "إضافة مبلغ للدين الحالي"
                ]
                debt_operation = st.selectbox(
                    "طريقة تحديث الدين",
                    debt_operation_options,
                    key="debt_operation_type"
                )

        col1, col2 = st.columns([3, 1])

        with col1:
            st.text_input("قيمة الدين (فلس)",
                         value=initial_debt,
                         key="debt_amount",
                         on_change=handle_enter_key,
                         placeholder="أدخل المبلغ بالفلس...")

        with col2:
            st.markdown("&nbsp;")  # مسافة فارغة للمحاذاة
            if is_existing_student:
                paid_operation_options = [
                    "تعيين قيمة جديدة",
                    "إضافة مبلغ للمسدد الحالي"
                ]
                paid_operation = st.selectbox(
                    "طريقة تحديث المسدد",
                    paid_operation_options,
                    key="paid_operation_type"
                )

        # حقل المبلغ المسدد والأزرار
        col1, col2 = st.columns([3, 1])

        with col1:
            st.text_input("المبلغ المسدد (فلس)",
                          value=initial_paid,
                          key="paid_amount",
                          on_change=handle_enter_key,
                          placeholder="أدخل المبلغ المسدد (إن وجد)...")

        with col2:
            st.markdown("&nbsp;")
            # أزرار لتسهيل التعامل مع المبالغ المسددة
            paid_quick_buttons = st.columns(2)
            if paid_quick_buttons[0].button("المبلغ كاملاً", key="pay_full"):
                if initial_debt:
                    st.session_state.paid_amount = initial_debt
                else:
                    st.session_state.paid_amount = st.session_state.debt_amount

            if paid_quick_buttons[1].button("لا شيء", key="pay_none"):
                st.session_state.paid_amount = "0"

        # أزرار لتنفيذ العمليات
        col1, col2 = st.columns(2)

        save_button = col1.button("💾 حفظ البيانات", type="primary")
        reset_button = col2.button("🔄 إلغاء", help="تنظيف النموذج وإلغاء التحديث")

        # إذا تم النقر على زر الحفظ
        if save_button:
            process_form_data()

        # إذا تم النقر على زر الإلغاء
        if reset_button:
            reset_form()
            st.rerun()

elif page == "عرض جميع الديون":
    st.header("عرض جميع سجلات الديون")

    # قسم عرض البيانات
    card(
        title="🔍 خيارات التصفية والبحث",
        content="<div id='filter-options'></div>",
        color="var(--primary)"
    )

    # الحصول على جميع سجلات الديون
    if not st.session_state.debt_manager.is_empty():
        df = st.session_state.debt_manager.get_dataframe()

        # إضافة خيارات التصفية والفرز
        filter_cols = st.columns([2, 2, 1])

        # البحث عن طالب محدد
        with filter_cols[0]:
            student_filter = st.text_input("البحث عن طالب", placeholder="اكتب جزء من اسم الطالب...")

        # تصفية حسب حالة السداد
        with filter_cols[1]:
            payment_status_options = ["الكل", "مسدد بالكامل", "مسدد جزئياً", "غير مسدد"]
            payment_filter = st.selectbox("تصفية حسب حالة السداد", payment_status_options)

        # فرز البيانات
        with filter_cols[2]:
            sort_options = ["الاسم (أبجدي)", "قيمة الدين (تنازلي)", "المتبقي (تنازلي)", "نسبة السداد (تنازلي)"]
            sort_by = st.selectbox("ترتيب حسب", sort_options)

        # تطبيق التصفية والفرز
        filtered_df = df.copy()

        # تصفية حسب اسم الطالب
        if student_filter:
            filtered_df = filtered_df[filtered_df['Student Name'].str.contains(student_filter, case=False)]

        # تصفية حسب حالة السداد
        if payment_filter != "الكل":
            if payment_filter == "مسدد بالكامل":
                filtered_df = filtered_df[filtered_df['Payment Percentage'] >= 100]
            elif payment_filter == "مسدد جزئياً":
                filtered_df = filtered_df[(filtered_df['Payment Percentage'] > 0) & (filtered_df['Payment Percentage'] < 100)]
            elif payment_filter == "غير مسدد":
                filtered_df = filtered_df[filtered_df['Payment Percentage'] == 0]

        # فرز البيانات
        if sort_by == "الاسم (أبجدي)":
            filtered_df = filtered_df.sort_values(by='Student Name')
        elif sort_by == "قيمة الدين (تنازلي)":
            filtered_df = filtered_df.sort_values(by='Debt Amount', ascending=False)
        elif sort_by == "المتبقي (تنازلي)":
            filtered_df = filtered_df.sort_values(by='Remaining Amount', ascending=False)
        elif sort_by == "نسبة السداد (تنازلي)":
            filtered_df = filtered_df.sort_values(by='Payment Percentage', ascending=False)

        # عرض إحصائيات مختصرة للبيانات المفلترة
        total_filtered_debt = filtered_df['Debt Amount'].sum()
        total_filtered_paid = filtered_df['Paid Amount'].sum()
        total_filtered_remaining = filtered_df['Remaining Amount'].sum()

        st.markdown("### إحصائيات البيانات المفلترة")
        stat_cols = st.columns(4)
        with stat_cols[0]:
            metric_card("عدد الطلاب", f"{len(filtered_df)}")
        with stat_cols[1]:
            metric_card("إجمالي الديون", format_currency(total_filtered_debt))
        with stat_cols[2]:
            metric_card("إجمالي المسدد", format_currency(total_filtered_paid))
        with stat_cols[3]:
            metric_card("إجمالي المتبقي", format_currency(total_filtered_remaining))

        # عرض البيانات المفلترة في جدول
        card(
            title="📊 قائمة الطلاب وديونهم",
            content="<div id='students-table'></div>",
            icon="👥",
            color="var(--secondary)"
        )

        # تنسيق العملة للعرض
        display_df = filtered_df.copy()
        display_df['Debt Amount'] = display_df['Debt Amount'].apply(format_currency)
        display_df['Paid Amount'] = display_df['Paid Amount'].apply(format_currency)
        display_df['Remaining Amount'] = display_df['Remaining Amount'].apply(format_currency)

        # التحقق من وجود عمود نسبة السداد قبل تطبيق التنسيق
        if 'Payment Percentage' in display_df.columns:
            display_df['Payment Percentage'] = display_df['Payment Percentage'].apply(lambda x: f"{x:.1f}%")

        # إعادة تسمية الأعمدة للعرض بالعربية
        display_df = display_df.rename(columns={
            'Student Name': 'اسم الطالب',
            'Debt Amount': 'قيمة الدين',
            'Paid Amount': 'المبلغ المسدد',
            'Remaining Amount': 'المبلغ المتبقي',
            'Payment Percentage': 'نسبة السداد'
        })

        # عرض الجدول
        st.dataframe(display_df, use_container_width=True)

        # إضافة زر طباعة الجدول
        create_print_button("طباعة جدول الطلاب المفلتر", "print_filtered_students")

        # إضافة زر لتنزيل البيانات المفلترة
        excel_data = io.BytesIO()
        filtered_df.to_excel(excel_data, index=False)
        excel_data.seek(0)

        st.download_button(
            label="تنزيل البيانات المفلترة (Excel)",
            data=excel_data,
            file_name=f"filtered_student_debts_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
            mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )

        # إضافة زر لتصدير تقرير PDF
        if st.button("📄 تصدير تقرير PDF لجميع الطلاب"):
            try:
                pdf_bytes = create_all_students_report(filtered_df)
                st.download_button(
                    label="تنزيل تقرير PDF",
                    data=pdf_bytes,
                    file_name=f"all_students_report_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
                    mime="application/pdf"
                )
                st.success("تم إنشاء تقرير PDF بنجاح!")
            except Exception as e:
                st.error(f"فشل إنشاء تقرير PDF: {str(e)}")
    else:
        info_box("لا توجد سجلات ديون حالياً. يرجى إضافة بعض البيانات أولاً.", "info")

elif page == "التقارير":
    st.header("تحليل وتقارير الديون")

    if not st.session_state.debt_manager.is_empty():
        # الحصول على البيانات
        df = st.session_state.debt_manager.get_dataframe()

        # القسم الأول: الرسوم البيانية
        card(
            title="📊 التحليل البياني للديون",
            content="<div id='debt-charts'></div>",
            color="var(--primary)"
        )

        # عرض الرسوم البيانية في صفين
        chart_row1 = st.columns(2)

        with chart_row1[0]:
            st.write("##### نسبة المبالغ المسددة وغير المسددة")
            # Usar la versión mejorada de la visualización
            fig_pie = create_enhanced_debt_visualization(df)
            st.plotly_chart(fig_pie, use_container_width=True)

        with chart_row1[1]:
            st.write("##### توزيع الديون حسب حالة السداد")
            # Usar la versión mejorada del gráfico de distribución
            fig_dist = create_enhanced_payment_status_chart(df)
            st.plotly_chart(fig_dist, use_container_width=True)

        # عرض أعلى الديون
        st.write("##### أعلى 5 ديون")
        top_debts_df = df.sort_values(by="Debt Amount", ascending=False).head(5)
        top_debts_display = top_debts_df.copy()
        top_debts_display['Debt Amount'] = top_debts_display['Debt Amount'].apply(format_currency)
        top_debts_display['Paid Amount'] = top_debts_display['Paid Amount'].apply(format_currency)
        top_debts_display['Remaining Amount'] = top_debts_display['Remaining Amount'].apply(format_currency)

        # إنشاء رسم شريطي لأعلى الديون
        fig_top_debts = px.bar(
            top_debts_df,
            x="Student Name",
            y=["Paid Amount", "Remaining Amount"],
            title="أعلى 5 طلاب من حيث قيمة الدين",
            labels={"value": "المبلغ", "Student Name": "اسم الطالب", "variable": "النوع"},
            color_discrete_map={"Paid Amount": "#4CAF50", "Remaining Amount": "#F44336"},
            barmode="stack"
        )
        st.plotly_chart(fig_top_debts, use_container_width=True)

        # القسم الثاني: تقارير حالة السداد
        card(
            title="📋 تقارير حالة السداد",
            content="<div id='payment-status-reports'></div>",
            icon="📊",
            color="var(--secondary)"
        )

        # الحصول على تقرير حالة السداد
        fully_paid_df, partially_paid_df, unpaid_df = st.session_state.debt_manager.get_payment_status_report()

        payment_tabs = st.tabs(["مسدد بالكامل", "مسدد جزئياً", "غير مسدد"])

        with payment_tabs[0]:
            if not fully_paid_df.empty:
                # تنسيق العملة للعرض
                display_df = fully_paid_df.copy()
                display_df['Debt Amount'] = display_df['Debt Amount'].apply(format_currency)
                display_df['Paid Amount'] = display_df['Paid Amount'].apply(format_currency)
                display_df['Remaining Amount'] = display_df['Remaining Amount'].apply(format_currency)

                # التحقق من وجود عمود نسبة السداد قبل تطبيق التنسيق
                if 'Payment Percentage' in display_df.columns:
                    display_df['Payment Percentage'] = display_df['Payment Percentage'].apply(lambda x: f"{x:.1f}%")

                # إعادة تسمية الأعمدة للعرض بالعربية
                display_df = display_df.rename(columns={
                    'Student Name': 'اسم الطالب',
                    'Debt Amount': 'قيمة الدين',
                    'Paid Amount': 'المبلغ المسدد',
                    'Remaining Amount': 'المبلغ المتبقي',
                    'Payment Percentage': 'نسبة السداد'
                })

                metric_card("عدد الطلاب المسددين بالكامل", f"{len(fully_paid_df)}")
                st.dataframe(display_df, use_container_width=True)

                # إضافة زر طباعة الجدول
                create_print_button("طباعة جدول الطلاب المسددين بالكامل", "print_fully_paid")
            else:
                info_box("لا يوجد طلاب مسددين بالكامل حالياً.", "info")

        with payment_tabs[1]:
            if not partially_paid_df.empty:
                # تنسيق العملة للعرض
                display_df = partially_paid_df.copy()
                display_df['Debt Amount'] = display_df['Debt Amount'].apply(format_currency)
                display_df['Paid Amount'] = display_df['Paid Amount'].apply(format_currency)
                display_df['Remaining Amount'] = display_df['Remaining Amount'].apply(format_currency)

                # التحقق من وجود عمود نسبة السداد قبل تطبيق التنسيق
                if 'Payment Percentage' in display_df.columns:
                    display_df['Payment Percentage'] = display_df['Payment Percentage'].apply(lambda x: f"{x:.1f}%")

                # إعادة تسمية الأعمدة للعرض بالعربية
                display_df = display_df.rename(columns={
                    'Student Name': 'اسم الطالب',
                    'Debt Amount': 'قيمة الدين',
                    'Paid Amount': 'المبلغ المسدد',
                    'Remaining Amount': 'المبلغ المتبقي',
                    'Payment Percentage': 'نسبة السداد'
                })

                # إحصائيات
                total_partial_debt = partially_paid_df['Debt Amount'].sum()
                total_partial_paid = partially_paid_df['Paid Amount'].sum()
                total_partial_remaining = partially_paid_df['Remaining Amount'].sum()
                avg_payment_percentage = partially_paid_df['Payment Percentage'].mean()

                metrics_col = st.columns(4)
                with metrics_col[0]:
                    metric_card("عدد الطلاب", f"{len(partially_paid_df)}")
                with metrics_col[1]:
                    metric_card("المتوسط المسدد", f"{avg_payment_percentage:.1f}", suffix="%")
                with metrics_col[2]:
                    metric_card("إجمالي المسدد", format_currency(total_partial_paid))
                with metrics_col[3]:
                    metric_card("إجمالي المتبقي", format_currency(total_partial_remaining))

                st.dataframe(display_df, use_container_width=True)

                # إضافة زر طباعة الجدول
                create_print_button("طباعة جدول الطلاب المسددين جزئياً", "print_partially_paid")
            else:
                info_box("لا يوجد طلاب مسددين جزئياً حالياً.", "info")

        with payment_tabs[2]:
            if not unpaid_df.empty:
                # تنسيق العملة للعرض
                display_df = unpaid_df.copy()
                display_df['Debt Amount'] = display_df['Debt Amount'].apply(format_currency)
                display_df['Paid Amount'] = display_df['Paid Amount'].apply(format_currency)
                display_df['Remaining Amount'] = display_df['Remaining Amount'].apply(format_currency)

                # التحقق من وجود عمود نسبة السداد قبل تطبيق التنسيق
                if 'Payment Percentage' in display_df.columns:
                    display_df['Payment Percentage'] = display_df['Payment Percentage'].apply(lambda x: f"{x:.1f}%")

                # إعادة تسمية الأعمدة للعرض بالعربية
                display_df = display_df.rename(columns={
                    'Student Name': 'اسم الطالب',
                    'Debt Amount': 'قيمة الدين',
                    'Paid Amount': 'المبلغ المسدد',
                    'Remaining Amount': 'المبلغ المتبقي',
                    'Payment Percentage': 'نسبة السداد'
                })

                # إحصائيات
                total_unpaid_debt = unpaid_df['Debt Amount'].sum()

                col1, col2 = st.columns(2)
                with col1:
                    metric_card("عدد الطلاب غير المسددين", f"{len(unpaid_df)}")
                with col2:
                    metric_card("إجمالي الديون غير المسددة", format_currency(total_unpaid_debt))

                st.dataframe(display_df, use_container_width=True)

                # إضافة زر طباعة الجدول
                create_print_button("طباعة جدول الطلاب غير المسددين", "print_unpaid")
            else:
                info_box("لا يوجد طلاب غير مسددين حالياً.", "info")

        # زر تنزيل تقرير حالة السداد
        if st.button("📄 تصدير تقرير حالة السداد (PDF)"):
            try:
                pdf_bytes = create_payment_status_report(fully_paid_df, partially_paid_df, unpaid_df)
                st.download_button(
                    label="تنزيل تقرير حالة السداد",
                    data=pdf_bytes,
                    file_name=f"payment_status_report_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
                    mime="application/pdf"
                )
                st.success("تم إنشاء تقرير PDF بنجاح!")
            except Exception as e:
                st.error(f"فشل إنشاء تقرير PDF: {str(e)}")
    else:
        info_box("لا توجد بيانات لعرض التقارير. يرجى إضافة بعض البيانات أولاً.", "warning")

elif page == "الفواتير":
    # استيراد وحدة الفواتير وسندات القبض
    try:
        from receipts import create_receipt_html, create_invoice_html, create_bulk_invoice_report_html, export_transactions_to_excel
    except ImportError:
        st.error("لم يتم العثور على وحدة الفواتير وسندات القبض.")

    st.header("إصدار الفواتير وسندات القبض")

    # التحقق من وجود سجلات ديون
    if not st.session_state.debt_manager.is_empty():
        # قسم إصدار الفواتير
        card(
            title="🧾 إصدار فاتورة دين أو تسديد",
            content="<div id='invoice-form'></div>",
            color="var(--primary)"
        )

        # اختيار نوع الفاتورة
        invoice_type = st.radio(
            "نوع الفاتورة",
            ["فاتورة دين", "فاتورة تسديد"],
            horizontal=True
        )

        # اختيار الطالب
        student_names = st.session_state.debt_manager.get_all_students()
        selected_student = st.selectbox("اختر الطالب", student_names)

        # الحصول على معلومات الطالب
        if selected_student:
            student_debt = st.session_state.debt_manager.get_debt(selected_student) or 0
            student_paid = st.session_state.debt_manager.get_paid_amount(selected_student) or 0
            student_remaining = student_debt - student_paid

            # عرض المعلومات الأساسية
            info_cols = st.columns(3)
            with info_cols[0]:
                metric_card("قيمة الدين", format_currency(student_debt))
            with info_cols[1]:
                metric_card("المبلغ المسدد", format_currency(student_paid))
            with info_cols[2]:
                metric_card("المبلغ المتبقي", format_currency(student_remaining))

            # الفترة الزمنية للفاتورة
            st.subheader("الفترة الزمنية للفاتورة")
            period_type = st.radio(
                "اختر نوع الفترة",
                ["يومية", "أسبوعية", "شهرية", "فترة محددة"],
                horizontal=True
            )

            # تحديد تاريخ البداية والنهاية بناءً على نوع الفترة
            today = datetime.datetime.now().date()
            end_date = today

            if period_type == "يومية":
                start_date = today
                date_label = f"فاتورة يوم {today.strftime('%Y-%m-%d')}"
            elif period_type == "أسبوعية":
                start_date = today - datetime.timedelta(days=7)
                date_label = f"فاتورة الأسبوع من {start_date.strftime('%Y-%m-%d')} إلى {today.strftime('%Y-%m-%d')}"
            elif period_type == "شهرية":
                start_date = today.replace(day=1)
                date_label = f"فاتورة شهر {today.strftime('%Y-%m')}"
            else:  # فترة محددة
                date_cols = st.columns(2)
                with date_cols[0]:
                    start_date = st.date_input("تاريخ البداية", value=today - datetime.timedelta(days=30))
                with date_cols[1]:
                    end_date = st.date_input("تاريخ النهاية", value=today)
                date_label = f"فاتورة الفترة من {start_date.strftime('%Y-%m-%d')} إلى {end_date.strftime('%Y-%m-%d')}"

            # تحويل التواريخ إلى نوع datetime للمقارنة مع تواريخ المعاملات
            start_datetime = datetime.datetime.combine(start_date, datetime.time.min)
            end_datetime = datetime.datetime.combine(end_date, datetime.time.max)

            # الحصول على المعاملات خلال الفترة المحددة
            all_transactions = st.session_state.debt_manager.get_student_transactions(selected_student)
            period_transactions = all_transactions[
                (all_transactions['Transaction Date'] >= start_datetime) &
                (all_transactions['Transaction Date'] <= end_datetime)
            ] if not all_transactions.empty else pd.DataFrame()

            # حساب إحصائيات الفترة
            if not period_transactions.empty:
                # فلترة المعاملات حسب نوع الفاتورة
                if invoice_type == "فاتورة دين":
                    filtered_transactions = period_transactions[period_transactions['Transaction Type'] == 'debt']
                    period_total = filtered_transactions['Amount'].sum() if not filtered_transactions.empty else 0
                    transaction_type_label = "إجمالي الديون"
                else:  # فاتورة تسديد
                    filtered_transactions = period_transactions[period_transactions['Transaction Type'] == 'payment']
                    period_total = filtered_transactions['Amount'].sum() if not filtered_transactions.empty else 0
                    transaction_type_label = "إجمالي التسديدات"

                # عرض الإحصائيات
                st.metric(transaction_type_label, format_currency(period_total))

                # عرض تفاصيل المعاملات
                if not filtered_transactions.empty:
                    st.subheader("تفاصيل المعاملات")

                    # تحضير البيانات للعرض
                    display_df = filtered_transactions.copy()
                    display_df['Amount'] = display_df['Amount'].apply(format_currency)
                    display_df['Transaction Date'] = display_df['Transaction Date'].dt.strftime('%Y-%m-%d %H:%M')

                    # إعادة تسمية الأعمدة بالعربية
                    display_df = display_df.rename(columns={
                        'Transaction Type': 'نوع المعاملة',
                        'Amount': 'المبلغ',
                        'Transaction Date': 'تاريخ المعاملة',
                        'Notes': 'ملاحظات'
                    })

                    # تغيير قيم نوع المعاملة إلى العربية
                    display_df['نوع المعاملة'] = display_df['نوع المعاملة'].replace({
                        'debt': 'دين',
                        'payment': 'تسديد'
                    })

                    # عرض الجدول
                    st.dataframe(display_df, use_container_width=True)

                    # زر طباعة الفاتورة
                    if st.button("🖨️ طباعة الفاتورة", type="primary"):
                        # استخدام الدالة الجديدة من وحدة الفواتير
                        invoice_html = create_invoice_html(
                            invoice_type=invoice_type,
                            student_name=selected_student,
                            student_debt=student_debt,
                            student_paid=student_paid,
                            period_total=period_total,
                            date_label=date_label,
                            transactions_df=filtered_transactions
                        )

                        # عرض الـ HTML مع تمكين JavaScript
                        st.markdown(invoice_html, unsafe_allow_html=True)
                        st.success("تم إنشاء الفاتورة وفتح نافذة الطباعة!")

                        # زر لتنزيل الفاتورة كملف Excel
                        excel_data = export_transactions_to_excel(filtered_transactions, selected_student)
                        if excel_data:
                            st.download_button(
                                label="تنزيل الفاتورة (Excel)",
                                data=excel_data,
                                file_name=f"{invoice_type}_{selected_student}_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
                                mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                            )
                else:
                    info_box(f"لا توجد معاملات من نوع {invoice_type.replace('فاتورة', '').strip()} خلال الفترة المحددة.", "warning")
            else:
                info_box(f"لا توجد معاملات للطالب {selected_student} خلال الفترة المحددة.", "warning")

        # قسم إصدار سندات القبض
        card(
            title="💰 إصدار سند قبض",
            content="<div id='receipt-form'></div>",
            icon="💵",
            color="var(--success)"
        )

        # اختيار الطالب لسند القبض
        receipt_student = st.selectbox("اختر الطالب", student_names, key="receipt_student_select")

        if receipt_student:
            receipt_debt = st.session_state.debt_manager.get_debt(receipt_student) or 0
            receipt_paid = st.session_state.debt_manager.get_paid_amount(receipt_student) or 0
            receipt_remaining = receipt_debt - receipt_paid

            # عرض معلومات الطالب
            receipt_info_cols = st.columns(3)
            with receipt_info_cols[0]:
                metric_card("قيمة الدين", format_currency(receipt_debt))
            with receipt_info_cols[1]:
                metric_card("المبلغ المسدد", format_currency(receipt_paid))
            with receipt_info_cols[2]:
                metric_card("المبلغ المتبقي", format_currency(receipt_remaining))

            # نموذج إدخال معلومات سند القبض
            receipt_amount = st.number_input(
                "المبلغ المستلم",
                min_value=0.1,
                max_value=float(receipt_remaining) if receipt_remaining > 0 else 999999.0,
                step=0.1,
                value=min(100.0, float(receipt_remaining)) if receipt_remaining > 0 else 100.0,
                format="%.1f"
            )

            receipt_date = st.date_input(
                "تاريخ الاستلام",
                value=datetime.datetime.now().date(),
                key="receipt_date_input"
            )

            receipt_notes = st.text_area(
                "ملاحظات",
                placeholder="أدخل أي ملاحظات متعلقة بسند القبض",
                value="سند قبض",
                key="receipt_notes_input"
            )

            receipt_payment_method = st.selectbox(
                "طريقة الدفع",
                options=["نقداً", "حوالة بنكية", "شيك", "بطاقة ائتمان", "أخرى"],
                key="receipt_payment_method"
            )

            if st.button("إصدار سند القبض وتسجيل الدفعة", type="primary", key="create_receipt_btn"):
                # إنشاء ملاحظات مفصلة
                detailed_notes = f"سند قبض: {receipt_notes} | طريقة الدفع: {receipt_payment_method} | تاريخ: {receipt_date.strftime('%Y-%m-%d')}"

                # تسجيل الدفعة في قاعدة البيانات
                payment_success = st.session_state.debt_manager.add_to_paid(
                    student_name=receipt_student,
                    amount_to_add=receipt_amount,
                    notes=detailed_notes
                )

                if payment_success:
                    # تحديث قيم الدين والمسدد بعد التسجيل
                    new_paid = st.session_state.debt_manager.get_paid_amount(receipt_student)

                    # إنشاء سند القبض
                    receipt_html = create_receipt_html(
                        student_name=receipt_student,
                        receipt_amount=receipt_amount,
                        debt_amount=receipt_debt,
                        paid_amount=receipt_paid,  # استخدم القيمة قبل الإضافة
                        receipt_date=receipt_date,
                        receipt_notes=receipt_notes,
                        receipt_payment_method=receipt_payment_method
                    )

                    # عرض رسالة نجاح
                    st.success(f"تم تسجيل الدفعة بنجاح وإصدار سند القبض للطالب {receipt_student}")
                    st.markdown(receipt_html, unsafe_allow_html=True)
                    st.rerun()
                else:
                    st.error("حدث خطأ أثناء تسجيل الدفعة. يرجى المحاولة مرة أخرى.")

        # قسم إصدار فواتير متعددة
        card(
            title="📊 تقرير فواتير إجمالي",
            content="<div id='bulk-invoice-report'></div>",
            icon="📈",
            color="var(--info)"
        )

        # اختيار الفترة الزمنية
        bulk_period_type = st.radio(
            "اختر نوع الفترة",
            ["يومية", "أسبوعية", "شهرية", "فترة محددة"],
            horizontal=True,
            key="bulk_period_type"
        )

        # تحديد الفترة الزمنية
        bulk_today = datetime.datetime.now().date()
        bulk_end_date = bulk_today

        if bulk_period_type == "يومية":
            bulk_start_date = bulk_today
            bulk_date_label = f"تقرير يوم {bulk_today.strftime('%Y-%m-%d')}"
        elif bulk_period_type == "أسبوعية":
            bulk_start_date = bulk_today - datetime.timedelta(days=7)
            bulk_date_label = f"تقرير الأسبوع من {bulk_start_date.strftime('%Y-%m-%d')} إلى {bulk_today.strftime('%Y-%m-%d')}"
        elif bulk_period_type == "شهرية":
            bulk_start_date = bulk_today.replace(day=1)
            bulk_date_label = f"تقرير شهر {bulk_today.strftime('%Y-%m')}"
        else:  # فترة محددة
            bulk_date_cols = st.columns(2)
            with bulk_date_cols[0]:
                bulk_start_date = st.date_input("تاريخ البداية", value=bulk_today - datetime.timedelta(days=30), key="bulk_start_date")
            with bulk_date_cols[1]:
                bulk_end_date = st.date_input("تاريخ النهاية", value=bulk_today, key="bulk_end_date")
            bulk_date_label = f"تقرير الفترة من {bulk_start_date.strftime('%Y-%m-%d')} إلى {bulk_end_date.strftime('%Y-%m-%d')}"

        # تحويل التواريخ إلى datetime للمقارنة
        bulk_start_datetime = datetime.datetime.combine(bulk_start_date, datetime.time.min)
        bulk_end_datetime = datetime.datetime.combine(bulk_end_date, datetime.time.max)

        # اختيار نوع المعاملات للتقرير
        bulk_transaction_type = st.radio(
            "نوع المعاملات",
            ["الديون", "التسديدات", "جميع المعاملات"],
            horizontal=True
        )

        # زر إنشاء التقرير
        if st.button("إنشاء تقرير الفواتير", type="primary"):
            # الحصول على كافة المعاملات لجميع الطلاب
            all_students_transactions = []

            for student in student_names:
                student_transactions = st.session_state.debt_manager.get_student_transactions(student)
                if not student_transactions.empty:
                    # إضافة عمود اسم الطالب
                    student_transactions = student_transactions.copy()
                    student_transactions['Student Name'] = student
                    all_students_transactions.append(student_transactions)

            if all_students_transactions:
                # دمج جميع المعاملات
                merged_transactions = pd.concat(all_students_transactions)

                # فلترة حسب الفترة الزمنية
                period_transactions = merged_transactions[
                    (merged_transactions['Transaction Date'] >= bulk_start_datetime) &
                    (merged_transactions['Transaction Date'] <= bulk_end_datetime)
                ]

                # فلترة حسب نوع المعاملة
                if bulk_transaction_type == "الديون":
                    filtered_transactions = period_transactions[period_transactions['Transaction Type'] == 'debt']
                    report_title = "تقرير الديون"
                elif bulk_transaction_type == "التسديدات":
                    filtered_transactions = period_transactions[period_transactions['Transaction Type'] == 'payment']
                    report_title = "تقرير التسديدات"
                else:
                    filtered_transactions = period_transactions
                    report_title = "تقرير كافة المعاملات"

                if not filtered_transactions.empty:
                    # عرض ملخص للتقرير
                    total_amount = filtered_transactions['Amount'].sum()
                    unique_students = filtered_transactions['Student Name'].nunique()
                    transaction_count = len(filtered_transactions)

                    st.subheader("ملخص التقرير")
                    summary_cols = st.columns(3)
                    with summary_cols[0]:
                        metric_card("عدد الطلاب", f"{unique_students}")
                    with summary_cols[1]:
                        metric_card("عدد المعاملات", f"{transaction_count}")
                    with summary_cols[2]:
                        metric_card("إجمالي المبالغ", format_currency(total_amount))

                    # تحضير البيانات للعرض
                    display_df = filtered_transactions.copy()
                    display_df['Amount'] = display_df['Amount'].apply(format_currency)
                    display_df['Transaction Date'] = display_df['Transaction Date'].dt.strftime('%Y-%m-%d %H:%M')

                    # إعادة تسمية الأعمدة بالعربية
                    display_df = display_df.rename(columns={
                        'Student Name': 'اسم الطالب',
                        'Transaction Type': 'نوع المعاملة',
                        'Amount': 'المبلغ',
                        'Transaction Date': 'تاريخ المعاملة',
                        'Notes': 'ملاحظات'
                    })

                    # تغيير قيم نوع المعاملة إلى العربية
                    display_df['نوع المعاملة'] = display_df['نوع المعاملة'].replace({
                        'debt': 'دين',
                        'payment': 'تسديد'
                    })

                    # عرض الجدول
                    st.dataframe(display_df, use_container_width=True)

                    # إضافة زر طباعة التقرير
                    if st.button("🖨️ طباعة تقرير الفواتير", key="print_bulk_report"):
                        # استخدام الدالة الجديدة لإنشاء تقرير متعدد
                        report_html = create_bulk_invoice_report_html(
                            report_title=report_title,
                            bulk_date_label=bulk_date_label,
                            total_amount=total_amount,
                            unique_students=unique_students,
                            transaction_count=transaction_count,
                            transactions_df=display_df
                        )

                        # عرض الـ HTML مع تمكين JavaScript
                        st.markdown(report_html, unsafe_allow_html=True)
                        st.success("تم إنشاء التقرير وفتح نافذة الطباعة!")

                    # زر تنزيل التقرير كملف Excel
                    excel_data = export_transactions_to_excel(filtered_transactions)
                    if excel_data:
                        st.download_button(
                            label="تنزيل التقرير (Excel)",
                            data=excel_data,
                            file_name=f"invoices_report_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
                            mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                        )
                else:
                    st.warning(f"لا توجد معاملات من نوع {bulk_transaction_type} خلال الفترة المحددة.")
            else:
                info_box("لا توجد معاملات مسجلة في النظام.", "warning")
    else:
        info_box("لا توجد سجلات ديون لإصدار الفواتير. يرجى إضافة بعض البيانات أولاً.", "info")

elif page == "استيراد/تصدير":
    st.header("استيراد وتصدير البيانات")

    # القسم الأول: استيراد البيانات من ملف Excel
    card(
        title="📥 استيراد البيانات من Excel",
        content="<div id='import-data'></div>",
        icon="📊",
        color="var(--primary)"
    )

    # توضيح حول صيغة الملف
    st.markdown("""
    يمكنك استيراد بيانات الديون من ملف Excel. يجب أن يكون الملف في إحدى الصيغ التالية:
    1. **الصيغة القياسية**: يحتوي على الأعمدة "اسم الطالب"، "قيمة الدين"، "المبلغ المسدد" (اختياري).
    2. **الصيغة المخصصة**: باستخدام نموذج الوزارة أو أي نموذج آخر، يمكنك تحديد أعمدة الأسماء والمبالغ يدوياً.
    """)

    # خيارات الاستيراد
    with st.expander("إعدادات الاستيراد", expanded=True):
        import_options_col1, import_options_col2 = st.columns(2)

        with import_options_col1:
            replace_existing = st.checkbox("استبدال جميع البيانات الحالية", value=False, help="سيتم حذف جميع البيانات الحالية قبل الاستيراد")

        with import_options_col2:
            update_existing = st.checkbox("تحديث البيانات الموجودة", value=True, help="تحديث سجلات الطلاب الموجودين بدلاً من تخطيها")

        # خيار استخدام تنسيق خاص
        use_specific_format = st.checkbox("استخدام تنسيق مخصص (نموذج الوزارة أو غيره)", value=False)

        if use_specific_format:
            spec_col1, spec_col2, spec_col3 = st.columns(3)

            with spec_col1:
                names_col = st.text_input("عمود أسماء الطلاب (مثل C)", value="C")

            with spec_col2:
                debts_col = st.text_input("عمود مبالغ الديون (مثل D)", value="D")

            with spec_col3:
                start_row = st.number_input("بدء من الصف رقم", min_value=1, value=2)

            end_row = st.number_input("انتهاء عند الصف رقم (اتركه 0 لاستيراد كافة الصفوف)", min_value=0, value=0)
            if end_row == 0:
                end_row = None

    # رفع الملف
    uploaded_file = st.file_uploader("اختر ملف Excel", type=["xlsx", "xls"])

    if uploaded_file is not None:
        try:
            if use_specific_format:
                # استيراد البيانات باستخدام التنسيق المخصص
                success = st.session_state.debt_manager.import_from_excel(
                    uploaded_file,
                    use_specific_format=True,
                    names_col=names_col,
                    debts_col=debts_col,
                    start_row=start_row,
                    end_row=end_row,
                    replace_existing=replace_existing,
                    update_existing=update_existing
                )
            else:
                # استيراد البيانات باستخدام التنسيق القياسي
                success = st.session_state.debt_manager.import_from_excel(
                    uploaded_file,
                    replace_existing=replace_existing,
                    update_existing=update_existing
                )

            if success:
                st.success("تم استيراد البيانات بنجاح!")
                # إعادة تحميل الصفحة لعرض البيانات الجديدة
                st.rerun()
            else:
                st.error("فشل استيراد البيانات. تأكد من صحة تنسيق الملف.")
        except Exception as e:
            st.error(f"حدث خطأ أثناء استيراد البيانات: {str(e)}")

    # القسم الثاني: تصدير البيانات إلى ملف Excel
    card(
        title="📤 تصدير البيانات إلى Excel",
        content="<div id='export-data'></div>",
        icon="💾",
        color="var(--secondary)"
    )

    if not st.session_state.debt_manager.is_empty():
        # زر لتصدير جميع البيانات
        if st.button("تصدير جميع بيانات الديون (Excel)"):
            # إنشاء ملف Excel في الذاكرة
            excel_data = io.BytesIO()
            df = st.session_state.debt_manager.get_dataframe()
            df.to_excel(excel_data, index=False)
            excel_data.seek(0)

            # إنشاء رابط تنزيل
            st.download_button(
                label="تنزيل ملف Excel",
                data=excel_data,
                file_name=f"student_debts_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
                mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            )
            st.success("تم إنشاء ملف Excel بنجاح!")
    else:
        info_box("لا توجد بيانات للتصدير. يرجى إضافة بعض البيانات أولاً.", "info")

    # إضافة زر لتنزيل قالب فارغ
    if st.button("تنزيل قالب Excel فارغ"):
        # إنشاء قالب فارغ
        excel_template = generate_excel_template()
        # إنشاء رابط تنزيل
        st.download_button(
            label="تنزيل القالب الفارغ",
            data=excel_template,
            file_name="نظام_إدارة_ديون_الطلاب.xlsx",
            mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            key="export_template_button"
        )
        st.success("تم إنشاء قالب Excel بنجاح!")

    # القسم الثالث: تنظيف البيانات
    card(
        title="🗑️ تنظيف البيانات",
        content="<div id='clear-data'></div>",
        icon="⚠️",
        color="var(--error)"
    )

    st.warning("سيؤدي هذا الإجراء إلى حذف جميع بيانات الديون. لا يمكن التراجع عن هذه العملية.")

    # زر مؤكد لحذف جميع البيانات
    clear_col1, clear_col2 = st.columns([3, 1])

    with clear_col1:
        confirm_text = st.text_input("اكتب 'أوافق على حذف جميع البيانات' للتأكيد", key="confirm_clear")

    with clear_col2:
        if st.button("حذف جميع البيانات", disabled=(confirm_text != "أوافق على حذف جميع البيانات")):
            if st.session_state.debt_manager.clear_all_data():
                st.success("تم حذف جميع البيانات بنجاح!")
                st.session_state.similar_names = []  # مسح نتائج البحث السابقة
                # إعادة تحميل الصفحة
                st.rerun()
            else:
                st.error("فشل حذف البيانات. حاول مرة أخرى.")