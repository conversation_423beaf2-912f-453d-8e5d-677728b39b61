import plotly.express as px
import plotly.graph_objects as go
import pandas as pd

def format_currency_hover(amount):
    """
    تنسيق المبلغ بالفلس للعرض في التلميحات
    
    Args:
        amount (float): المبلغ بالفلس مباشرة
        
    Returns:
        str: النص المنسق بالفلس
    """
    fils_amount = int(amount)
    return f"{fils_amount:,} فلس"

def create_debt_visualization(df):
    """
    إنشاء تصور رسم بياني شريطي لديون الطلاب
    
    Args:
        df (DataFrame): إطار بيانات يحتوي على بيانات ديون الطلاب
        
    Returns:
        Figure: كائن الرسم البياني في Plotly
    """
    # ترتيب حسب قيمة الدين للحصول على تصور أفضل
    sorted_df = df.sort_values(by='Debt Amount', ascending=False)
    
    # إنشاء رسم بياني شريطي
    fig = px.bar(
        sorted_df,
        x='Student Name',
        y='Debt Amount',
        title='نظرة عامة على ديون الطلاب',
        labels={'Student Name': 'الطالب', 'Debt Amount': 'قيمة الدين (فلس)'},
        color='Debt Amount',
        color_continuous_scale='Viridis',
        custom_data=[sorted_df['Debt Amount']]  # استخدام القيمة كما هي بدون تحويل
    )
    
    # تحديث التخطيط لتحسين القراءة
    fig.update_layout(
        xaxis_title='الطالب',
        yaxis_title='قيمة الدين (فلس)',
        xaxis_tickangle=-45,
        hovermode='closest'
    )
    
    # تحديث قالب التمرير لعرض القيم بالفلس فقط
    fig.update_traces(
        hovertemplate='<b>%{x}</b><br>%{customdata[0]:,.0f} فلس'
    )
    
    return fig

def create_debt_distribution_chart(df):
    """
    إنشاء مخطط دائري يوضح توزيع الديون بين الطلاب
    
    Args:
        df (DataFrame): إطار بيانات يحتوي على بيانات ديون الطلاب
        
    Returns:
        Figure: كائن الرسم البياني في Plotly
    """
    # حساب النسبة المئوية لإجمالي الدين لكل طالب
    total_debt = df['Debt Amount'].sum()
    df_with_percentage = df.copy()
    df_with_percentage['Percentage'] = (df['Debt Amount'] / total_debt) * 100
    
    # استخدام قيم الدين مباشرة للعرض في التلميحات
    df_with_percentage['Fils'] = df_with_percentage['Debt Amount']
    
    # إذا كان هناك العديد من الطلاب، قم بتحديد المخطط الدائري إلى أعلى 10
    if len(df_with_percentage) > 10:
        top_students = df_with_percentage.sort_values(by='Debt Amount', ascending=False).head(9)
        
        # حساب المجموع للطلاب الآخرين
        others_debt = df_with_percentage.iloc[9:]['Debt Amount'].sum()
        others_percentage = df_with_percentage.iloc[9:]['Percentage'].sum()
        others_fils = df_with_percentage.iloc[9:]['Fils'].sum()
        
        others = pd.DataFrame({
            'Student Name': ['آخرون'],
            'Debt Amount': [others_debt],
            'Percentage': [others_percentage],
            'Fils': [others_fils]
        })
        
        df_for_chart = pd.concat([top_students, others], ignore_index=True)
    else:
        df_for_chart = df_with_percentage
    
    # إنشاء مخطط دائري
    fig = px.pie(
        df_for_chart, 
        values='Debt Amount', 
        names='Student Name',
        title='توزيع الديون بين الطلاب',
        hover_data=['Percentage', 'Fils'],
        labels={'Percentage': 'نسبة من إجمالي الدين', 'Fils': 'القيمة بالفلس'},
    )
    
    # تحديث قالب التمرير لعرض القيم بالفلس فقط
    fig.update_traces(
        hovertemplate='<b>%{label}</b><br>' +
        'القيمة: %{customdata[1]:,.0f} فلس<br>' +
        'النسبة: %{customdata[0]:.1f}%'
    )
    
    # تحديث التخطيط لمظهر أفضل
    fig.update_layout(
        legend_title_text='الطلاب'
    )
    
    return fig

def create_debt_trends_chart(historical_data):
    """
    إنشاء مخطط خطي يوضح اتجاهات الديون بمرور الوقت
    
    Args:
        historical_data (DataFrame): إطار بيانات يحتوي على بيانات الديون التاريخية
        
    Returns:
        Figure: كائن الرسم البياني في Plotly
    """
    # استخدام قيم الدين مباشرة للعرض في التلميحات
    trend_data = historical_data.copy()
    trend_data['Fils'] = trend_data['Total Debt']
    
    # إنشاء مخطط خطي
    fig = px.line(
        trend_data,
        x='Date',
        y='Total Debt',
        title='اتجاهات الديون بمرور الوقت',
        labels={'Total Debt': 'إجمالي قيمة الدين (فلس)', 'Date': 'التاريخ'},
        custom_data=['Fils']
    )
    
    # تحديث التخطيط لتحسين القراءة
    fig.update_layout(
        xaxis_title='التاريخ',
        yaxis_title='إجمالي قيمة الدين (فلس)',
        hovermode='x unified'
    )
    
    # تحديث قالب التمرير لعرض القيم بالفلس فقط
    fig.update_traces(
        hovertemplate='<b>%{x}</b><br>' +
        'القيمة: %{customdata[0]:,.0f} فلس'
    )
    
    return fig
