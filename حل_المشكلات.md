# دليل حل المشكلات الشائعة

هذا الدليل سيساعدك في حل المشكلات الشائعة التي قد تواجهها عند استخدام أو تثبيت نظام إدارة ديون الطلاب.

## مشكلات التثبيت

### خطأ: فشل تثبيت المكتبات المطلوبة

#### المشكلة
فشل تثبيت مكتبات بايثون المطلوبة مع ظهور أخطاء متعلقة بالمتطلبات أو حقوق الوصول.

#### الحل
1. تأكد من وجود بايثون 3.10 أو أحدث:
   ```bash
   python3 --version
   ```

2. قم بتثبيت أدوات التطوير الأساسية:
   ```bash
   # على Ubuntu/Debian
   sudo apt update
   sudo apt install python3-dev python3-pip python3-venv build-essential
   
   # على Fedora/CentOS
   sudo dnf install python3-devel python3-pip gcc
   ```

3. استخدم بيئة افتراضية لتجنب مشكلات الأذونات:
   ```bash
   python3 -m venv venv
   source venv/bin/activate
   pip install --upgrade pip
   pip install streamlit pandas plotly sqlalchemy psycopg2-binary openpyxl
   ```

### خطأ: فشل الاتصال بقاعدة البيانات PostgreSQL

#### المشكلة
يفشل التطبيق في الاتصال بقاعدة البيانات مع ظهور رسائل خطأ متعلقة بالاتصال.

#### الحل
1. تحقق من تثبيت وتشغيل PostgreSQL:
   ```bash
   sudo systemctl status postgresql
   ```

2. تأكد من وجود قاعدة البيانات والمستخدم:
   ```bash
   sudo -u postgres psql -c "SELECT datname FROM pg_database;"
   sudo -u postgres psql -c "SELECT usename FROM pg_user;"
   ```

3. تأكد من صحة بيانات الاتصال في ملف `.env`:
   ```bash
   cat .env
   ```

4. تحقق من إعدادات الاتصال والأذونات لقاعدة البيانات:
   ```bash
   sudo nano /etc/postgresql/*/main/pg_hba.conf
   ```

## مشكلات التشغيل

### خطأ: التطبيق لا يبدأ التشغيل

#### المشكلة
عند تشغيل الأمر `streamlit run app.py`، لا يبدأ التطبيق أو يتوقف مباشرة.

#### الحل
1. تحقق من سجلات الخطأ:
   ```bash
   streamlit run app.py 2> error.log
   cat error.log
   ```

2. تأكد من تثبيت جميع المكتبات المطلوبة:
   ```bash
   pip install -r requirements.txt
   ```

3. تحقق من وجود جميع ملفات المشروع الأساسية:
   ```bash
   ls -la
   ```

### خطأ: مشكلات في العرض أو التنسيق

#### المشكلة
بعض عناصر واجهة المستخدم لا تظهر بشكل صحيح أو تظهر مشكلات في تنسيق النص العربي.

#### الحل
1. تأكد من استخدام متصفح حديث متوافق مع Streamlit (مثل Chrome أو Firefox الحديث)
2. قم بتحديث مكتبة Streamlit للإصدار الأحدث:
   ```bash
   pip install --upgrade streamlit
   ```
3. تحقق من إعدادات اللغة والمنطقة في النظام:
   ```bash
   locale
   ```

### خطأ: البيانات لا تُحفظ في قاعدة البيانات

#### المشكلة
عند إضافة أو تحديث بيانات، لا تظهر التغييرات عند إعادة تشغيل التطبيق.

#### الحل
1. تحقق من أذونات الكتابة في قاعدة البيانات:
   ```bash
   sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE student_debts TO username;"
   ```

2. تحقق من وجود أخطاء في سجلات التطبيق:
   ```bash
   streamlit run app.py 2> error.log
   ```

3. تأكد من أن جلسة قاعدة البيانات تتم ببيانات الاعتماد الصحيحة.

## مشكلات استيراد/تصدير البيانات

### خطأ: فشل استيراد ملف Excel

#### المشكلة
عند محاولة استيراد بيانات من ملف Excel، تظهر رسالة خطأ.

#### الحل
1. تأكد من تنسيق ملف Excel الصحيح:
   - يجب أن يحتوي على الأعمدة المطلوبة: `Student Name`, `Debt Amount`
   - تأكد من أن القيم في عمود `Debt Amount` هي أرقام وليست نصوصًا

2. استخدم قالب Excel المتوفر مع النظام:
   - انقر على زر "تحميل قالب فارغ" في صفحة الاستيراد/التصدير

3. تأكد من تثبيت مكتبة openpyxl:
   ```bash
   pip install openpyxl
   ```

### خطأ: فشل تصدير البيانات إلى Excel

#### المشكلة
عند محاولة تصدير البيانات إلى ملف Excel، تظهر رسالة خطأ.

#### الحل
1. تحقق من أذونات الكتابة في المجلد الحالي:
   ```bash
   ls -la
   ```

2. تأكد من عدم فتح ملف التصدير في تطبيق آخر.

3. تأكد من تثبيت مكتبة openpyxl:
   ```bash
   pip install openpyxl
   ```

## مشكلات عرض الرسوم البيانية

### خطأ: الرسوم البيانية لا تظهر

#### المشكلة
لا تظهر الرسوم البيانية في صفحة "عرض جميع الديون" أو "التقارير".

#### الحل
1. تحقق من تثبيت مكتبة plotly:
   ```bash
   pip install plotly
   ```

2. تأكد من وجود بيانات في قاعدة البيانات:
   ```bash
   python -c "from database import DatabaseManager; db = DatabaseManager(); print(db.get_all_students())"
   ```

3. تحقق من وجود أخطاء في وحدة visualization.py:
   ```bash
   python -c "from visualization import create_debt_visualization; print('Module loaded successfully')"
   ```

## مشكلات أمان وإذن الوصول

### خطأ: رفض الاتصال عند الوصول عبر الويب

#### المشكلة
عند محاولة الوصول إلى التطبيق من جهاز آخر على الشبكة، يظهر خطأ "Connection Refused".

#### الحل
1. تأكد من تشغيل التطبيق مع تحديد عنوان وصول يسمح بالاتصالات الخارجية:
   ```bash
   streamlit run app.py --server.port 8501 --server.address 0.0.0.0
   ```

2. تحقق من إعدادات جدار الحماية:
   ```bash
   # على Ubuntu/Debian
   sudo ufw status
   sudo ufw allow 8501/tcp
   
   # على Fedora/CentOS
   sudo firewall-cmd --list-all
   sudo firewall-cmd --permanent --add-port=8501/tcp
   sudo firewall-cmd --reload
   ```

3. إذا كنت تستخدم AWS EC2 أو خدمة سحابية أخرى، تأكد من تكوين مجموعات الأمان للسماح بالوصول إلى المنفذ 8501.

## مشكلات متعلقة باللغة العربية

### خطأ: الكتابة العربية تظهر بشكل غير صحيح

#### المشكلة
النصوص العربية تظهر بشكل مشوه أو مقلوب.

#### الحل
1. تأكد من استخدام متصفح حديث يدعم اللغة العربية بشكل كامل.
2. تحقق من إعدادات اللغة والتشفير في النظام:
   ```bash
   locale
   # تأكد من وجود دعم UTF-8
   ```
3. إذا كان يتم تشغيل التطبيق على خادم بعيد، تأكد من تثبيت حزم دعم اللغة العربية:
   ```bash
   # على Ubuntu/Debian
   sudo apt install language-pack-ar
   ```

## مشكلات في التحديث

### خطأ: فشل تحديث التطبيق

#### المشكلة
عند تحديث ملفات التطبيق، تظهر أخطاء أو لا يعمل التطبيق بشكل صحيح.

#### الحل
1. احتفظ بنسخة احتياطية من الملفات القديمة قبل التحديث.
2. تأكد من عمل نسخة احتياطية من قاعدة البيانات قبل أي تحديث:
   ```bash
   pg_dump -U username -d student_debts > backup_before_update.sql
   ```
3. تأكد من توافق جميع الملفات المحدثة مع بعضها البعض.
4. بعد التحديث، قم بإعادة تثبيت المكتبات المطلوبة:
   ```bash
   pip install -r requirements.txt
   ```

## الدعم الإضافي

إذا استمرت المشكلة بعد محاولة الحلول المذكورة أعلاه، يمكنك:

1. مراجعة توثيق المشروع الشامل في ملف README.md
2. مراجعة سجلات الخطأ الكاملة للتطبيق وقاعدة البيانات
3. تحقق من إصدارات المكتبات المثبتة ومدى توافقها:
   ```bash
   pip freeze
   ```
4. طلب المساعدة من فريق الدعم أو مراجعة الشفرة المصدرية لمعرفة المزيد عن المشكلة