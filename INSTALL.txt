تعليمات تثبيت نظام إدارة ديون الطلاب
=================================

خطوات التثبيت السريعة:

1. تثبيت المتطلبات الأساسية:
   - تثبيت بايثون 3.10 أو أحدث
   - تثبيت PostgreSQL

2. إنشاء قاعدة بيانات PostgreSQL:
   - قم بإنشاء قاعدة بيانات جديدة
   - سجّل بيانات الاتصال (اسم المستخدم، كلمة المرور، الخ)

3. فك ضغط ملفات النظام:
   ```
   unzip student_debt_system.zip -d /path/to/destination
   cd /path/to/destination
   ```

4. تثبيت المكتبات المطلوبة:
   ```
   pip install streamlit pandas plotly sqlalchemy psycopg2-binary openpyxl
   ```

5. إعداد متغيرات البيئة وقاعدة البيانات:
   ```
   python setup_database.py --create-env --username=db_user --password=db_pass --dbname=student_debts
   python setup_database.py --setup-db
   ```

6. تشغيل التطبيق:
   ```
   streamlit run app.py --server.port 8501 --server.address 0.0.0.0
   ```

7. الوصول إلى التطبيق:
   افتح المتصفح وانتقل إلى: http://your_server_ip:8501

لمزيد من التفاصيل والخيارات المتقدمة، راجع ملف README.md