script>
    // إنشاء معالج النقر باستخدام استدعاء مباشر لتجنب خطأ React
    (function() {
        document.getElementById("print_button_print_filtered_students").addEventListener("click", function() {
            // إنشاء نافذة طباعة جديدة
            var printWindow = window.open('', '_blank');
            var tables = document.querySelectorAll('.stDataFrame table');

            // إنشاء محتوى الصفحة
            var htmlContent = `
                <html>
                <head>
                    <title>طباعة جدول ديون الطلاب</title>
                    <style>
                        @media print {
                            body { font-family: Arial, sans-serif; direction: rtl; }
                            h2 { color: #1E6496; font-size: 18px; text-align: center; margin-bottom: 15px; }
                            table { width: 100%; border-collapse: collapse; margin-bottom: 15px; }
                            th { background-color: #E0E0E0; padding: 8px; text-align: right; border: 1px solid #888; }
                            td { padding: 6px; text-align: right; border: 1px solid #CCCCCC; }
                            tr:nth-child(even) { background-color: #f2f2f2; }
                        }
                    </style>
                </head>
                <body>
                    <h2>بيانات ديون الطلاب</h2>
                    <p>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-EG')}</p>
            `;

            // نسخ محتوى الجداول
            if (tables.length > 0) {
                for (var i = 0; i < tables.length; i++) {
                    htmlContent += '<div>' + tables[i].outerHTML + '</div>';
                }
            } else {
                htmlContent += '<p>لا توجد بيانات للطباعة.</p>';
            }

            // إغلاق المستند
            htmlContent += `</body></html>`;

            // كتابة المحتوى في نافذة الطباعة
            printWindow.document.write(htmlContent);
            printWindow.document.close();

            // طباعة بعد تحميل الصفحة
            printWindow.onload = function() {
                setTimeout(function() {
                    printWindow.print();
                    printWindow.close();
                }, 300);
            };
        });
    })();
</script>