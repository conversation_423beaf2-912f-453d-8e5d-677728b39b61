"""
وحدة مبسطة للغاية لإنشاء تقارير PDF بدون استخدام f-strings أو تنسيقات متغيرة
"""
import os
import base64
import tempfile
from datetime import datetime
import io
import pandas as pd
from fpdf import FPDF
import streamlit as st

from utils import format_currency

class BasicPDF(FPDF):
    """فئة FPDF مبسطة تستخدم طرق آمنة لدمج النصوص"""
    
    def __init__(self):
        # تهيئة بالإعدادات الافتراضية
        super().__init__(orientation='P', unit='mm', format='A4')
        # إضافة خط يدعم العربية - يجب أن يكون الملف في نفس المجلد
        self.add_font('DejaVu', '', os.path.join(os.path.dirname(__file__), 'DejaVuSansCondensed.ttf'), uni=True)
        self.set_auto_page_break(auto=True, margin=15)
        self.add_page()
        self.set_font('DejaVu', '', 14)
    
    def header(self):
        """ترويسة الصفحة"""
        # تعيين الخط للترويسة
        self.set_font('DejaVu', '', 12)
        # العنوان (مركز النص)
        self.cell(0, 10, 'نظام إدارة ديون الطلاب', 0, 0, 'C')
        # تاريخ التقرير (يمين الصفحة)
        self.set_xy(10, 10)
        report_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        self.cell(0, 10, report_date, 0, 0, 'R')
        # الخط الفاصل
        self.line(10, 20, 200, 20)
        self.ln(15)
    
    def footer(self):
        """تذييل الصفحة"""
        # الانتقال إلى موضع 1.5 سم من أسفل الصفحة
        self.set_y(-15)
        # تعيين خط الطباعة
        self.set_font('DejaVu', '', 8)
        # رقم الصفحة (وسط الصفحة)
        page_number = self.page_no()
        self.cell(0, 10, f'الصفحة {page_number}', 0, 0, 'C')

def create_pdf_buffer(content_function):
    """
    دالة مساعدة لإنشاء كائن ذاكرة PDF بطريقة آمنة - نسخة محسنة مع معالجة أفضل للأخطاء
    
    Args:
        content_function: دالة تضيف محتوى إلى كائن PDF
        
    Returns:
        BytesIO: كائن ذاكرة يحتوي على ملف PDF أو None إذا فشلت العملية
    """
    try:
        # إنشاء وتعبئة محتوى PDF
        pdf = content_function()
        
        # تحقق من أن الـ PDF تم إنشاؤه بنجاح
        if pdf is None:
            print("فشل إنشاء كائن PDF")
            # إنشاء PDF بسيط يحتوي على رسالة خطأ
            error_pdf = BasicPDF()
            error_pdf.add_page()
            error_pdf.set_font('DejaVu', 'B', 16)
            error_pdf.cell(0, 10, 'خطأ في إنشاء التقرير', 0, 1, 'C')
            error_pdf.set_font('DejaVu', '', 12)
            error_pdf.cell(0, 10, 'لا توجد بيانات كافية لإنشاء التقرير.', 0, 1, 'C')
            pdf = error_pdf
        
        # استخدام BytesIO بدلاً من التعامل مع ملفات مؤقتة
        pdf_buffer = io.BytesIO()
        
        # محاولة إنشاء الملف مع معالجة الاستثناءات المحتملة
        try:
            # استخدام نسخة dest='S' وتحويلها إلى BytesIO
            pdf_binary = pdf.output(dest='S').encode('latin-1')
            pdf_buffer = io.BytesIO(pdf_binary)
            pdf_buffer.seek(0)
        except Exception as e:
            print(f"خطأ أثناء إنشاء ملف الـ PDF: {str(e)}")
            # إنشاء PDF بسيط يحتوي على رسالة خطأ
            error_pdf = BasicPDF()
            error_pdf.add_page()
            error_pdf.set_font('DejaVu', 'B', 16)
            error_pdf.cell(0, 10, 'خطأ في إنشاء التقرير', 0, 1, 'C')
            error_pdf.set_font('DejaVu', '', 12)
            error_pdf.cell(0, 10, f'حدث خطأ أثناء إنشاء التقرير: {str(e)}', 0, 1, 'C')
            
            # محاولة إنشاء ملف خطأ
            try:
                # استخدام نسخة dest='S' وتحويلها إلى BytesIO
                pdf_binary = error_pdf.output(dest='S').encode('latin-1')
                pdf_buffer = io.BytesIO(pdf_binary)
                pdf_buffer.seek(0)
            except Exception as e:
                print(f"خطأ في إنشاء ملف خطأ PDF: {str(e)}")
                # إذا فشلت كل المحاولات
                return None
        
        # التحقق من أن الملف تم إنشاؤه بنجاح وله حجم
        if pdf_buffer.getbuffer().nbytes > 0:
            return pdf_buffer
        else:
            print("تم إنشاء ملف PDF فارغ")
            return None
            
    except TypeError as e:
        print(f"خطأ في أنواع البيانات أثناء إنشاء PDF: {str(e)}")
        # محاولة إنشاء PDF بسيط يوضح الخطأ
        try:
            error_pdf = BasicPDF()
            error_pdf.add_page()
            error_pdf.set_font('DejaVu', 'B', 16)
            error_pdf.cell(0, 10, 'خطأ في أنواع البيانات', 0, 1, 'C')
            error_pdf.set_font('DejaVu', '', 12)
            error_pdf.cell(0, 10, f'حدث خطأ أثناء معالجة البيانات: {str(e)}', 0, 1, 'C')
            
            try:
                pdf_binary = error_pdf.output(dest='S').encode('latin-1')
                pdf_buffer = io.BytesIO(pdf_binary)
                pdf_buffer.seek(0)
                return pdf_buffer
            except Exception as e:
                print(f"خطأ في إنشاء PDF للحالة الاستثنائية: {str(e)}")
                return None
        except:
            return None
    
    except ValueError as e:
        print(f"خطأ في القيم أثناء إنشاء PDF: {str(e)}")
        return None
    
    except Exception as e:
        print(f"خطأ في إنشاء ملف PDF: {str(e)}")
        return None

def export_all_students_pdf(df):
    """
    تصدير بيانات جميع الطلاب إلى ملف PDF - نسخة محسنة مع تحقق أفضل من البيانات
    
    Args:
        df (DataFrame): بيانات الطلاب
        
    Returns:
        BytesIO: كائن ذاكرة يحتوي على ملف PDF
    """
    def create_content():
        # التحقق من البيانات - إذا كان الإطار فارغًا، إنشاء PDF بسيط بهذه المعلومة
        if df is None or df.empty:
            pdf = BasicPDF()
            pdf.set_font('DejaVu', 'B', 16)
            pdf.cell(0, 10, 'تقرير جميع الطلاب', 0, 1, 'C')
            pdf.ln(10)
            pdf.set_font('DejaVu', '', 12)
            pdf.cell(0, 10, 'لا توجد بيانات لعرضها.', 0, 1, 'C')
            pdf.ln(5)
            pdf.cell(0, 10, 'الرجاء إضافة بيانات أولاً ثم إعادة محاولة إنشاء التقرير.', 0, 1, 'C')
            return pdf
        
        # التأكد من وجود كل الأعمدة المطلوبة
        required_columns = ['Student Name', 'Debt Amount', 'Paid Amount', 'Remaining Amount']
        for col in required_columns:
            if col not in df.columns:
                pdf = BasicPDF()
                pdf.set_font('DejaVu', 'B', 16)
                pdf.cell(0, 10, 'خطأ في بيانات التقرير', 0, 1, 'C')
                pdf.ln(10)
                pdf.set_font('DejaVu', '', 12)
                pdf.cell(0, 10, f'عمود البيانات المطلوب غير موجود: {col}', 0, 1, 'C')
                return pdf
        
        # إنشاء كائن PDF جديد
        pdf = BasicPDF()
        
        # العنوان
        pdf.set_font('DejaVu', '', 16)
        pdf.cell(0, 10, 'تقرير جميع الطلاب', 0, 1, 'C')
        pdf.ln(5)
        
        # إضافة معلومات التاريخ والوقت
        pdf.set_font('DejaVu', '', 10)
        current_date = datetime.now().strftime('%Y-%m-%d %H:%M')
        pdf.cell(0, 10, f'تاريخ التقرير: {current_date}', 0, 1, 'L')
        pdf.ln(5)
        
        # معلومات إجمالية
        pdf.set_font('DejaVu', '', 12)
        pdf.cell(0, 10, 'ملخص', 0, 1, 'R')
        
        pdf.set_font('DejaVu', '', 10)
        
        # حساب المجاميع مع التحقق من البيانات
        try:
            # استخدام طريقة آمنة للمجاميع في حال وجود قيم فارغة أو غير صالحة
            total_debt = df['Debt Amount'].fillna(0).astype(float).sum()
            total_paid = df['Paid Amount'].fillna(0).astype(float).sum()
            total_remaining = df['Remaining Amount'].fillna(0).astype(float).sum()
            
            # عرض المجاميع
            pdf.cell(70, 8, 'إجمالي مبلغ الدين:', 0, 0, 'R')
            pdf.cell(0, 8, format_currency(total_debt), 0, 1, 'R')
            
            pdf.cell(70, 8, 'إجمالي المبلغ المسدد:', 0, 0, 'R')
            pdf.cell(0, 8, format_currency(total_paid), 0, 1, 'R')
            
            pdf.cell(70, 8, 'إجمالي المبلغ المتبقي:', 0, 0, 'R')
            pdf.cell(0, 8, format_currency(total_remaining), 0, 1, 'R')
            
            # إضافة عدد الطلاب
            pdf.cell(70, 8, 'عدد الطلاب:', 0, 0, 'R')
            pdf.cell(0, 8, str(len(df)), 0, 1, 'R')
        except Exception as e:
            # في حال حدوث خطأ في حساب المجاميع
            pdf.cell(0, 8, f'خطأ في حساب المجاميع: {str(e)}', 0, 1, 'C')
        
        # رؤوس الجدول
        pdf.ln(10)
        pdf.set_font('DejaVu', '', 10)
        cell_width = 30
        pdf.cell(30, 8, 'نسبة السداد', 1, 0, 'C')
        pdf.cell(cell_width, 8, 'المبلغ المتبقي', 1, 0, 'C')
        pdf.cell(cell_width, 8, 'المبلغ المسدد', 1, 0, 'C')
        pdf.cell(cell_width, 8, 'مبلغ الدين', 1, 0, 'C')
        pdf.cell(cell_width, 8, 'اسم الطالب', 1, 1, 'C')
        
        # بيانات الجدول
        pdf.set_font('DejaVu', '', 10)
        for _, row in df.iterrows():
            try:
                # الحصول على قيم العمود مع التحقق من صحتها
                student_name = str(row['Student Name']) if row['Student Name'] is not None else "غير معروف"
                
                # حساب نسبة السداد بطريقة آمنة
                try:
                    debt_amount = float(row['Debt Amount']) if row['Debt Amount'] is not None else 0
                except (ValueError, TypeError):
                    debt_amount = 0
                    
                try:
                    paid_amount = float(row['Paid Amount']) if row['Paid Amount'] is not None else 0
                except (ValueError, TypeError):
                    paid_amount = 0
                
                try:
                    remaining_amount = float(row['Remaining Amount']) if row['Remaining Amount'] is not None else 0
                except (ValueError, TypeError):
                    remaining_amount = debt_amount - paid_amount
                
                # تجنب القسمة على الصفر
                if debt_amount > 0:
                    payment_percentage = (paid_amount / debt_amount) * 100
                else:
                    payment_percentage = 0
                    
                # طباعة بيانات الطالب
                percentage_text = f"{round(payment_percentage, 1)}%"
                pdf.cell(30, 8, percentage_text, 1, 0, 'C')
                pdf.cell(cell_width, 8, format_currency(remaining_amount), 1, 0, 'C')
                pdf.cell(cell_width, 8, format_currency(paid_amount), 1, 0, 'C')
                pdf.cell(cell_width, 8, format_currency(debt_amount), 1, 0, 'C')
                pdf.cell(cell_width, 8, student_name, 1, 1, 'R')
            except Exception as e:
                # في حال حدوث خطأ في عرض بيانات الصف، نتخطاه
                continue
        
        # إضافة ملاحظة في نهاية التقرير
        pdf.ln(10)
        pdf.set_font('DejaVu', 'I', 8)
        pdf.cell(0, 10, 'المبالغ موضحة بالفلس الكويتي.', 0, 1, 'C')
        
        return pdf
    
    return create_pdf_buffer(create_content)

def export_student_report_pdf(student_name, debt_data, transactions_df):
    """
    تصدير تقرير تفصيلي لطالب معين إلى ملف PDF - نسخة محسنة مع تحقق أفضل من البيانات
    
    Args:
        student_name (str): اسم الطالب
        debt_data (dict): بيانات دين الطالب
        transactions_df (DataFrame): سجل معاملات الطالب
        
    Returns:
        BytesIO: كائن ذاكرة يحتوي على ملف PDF
    """
    def create_content():
        # التحقق من البيانات
        if student_name is None or student_name == "":
            student_name = "طالب غير معروف"
            
        if debt_data is None:
            debt_data = {}
            
        # إنشاء كائن PDF جديد
        pdf = BasicPDF()
        
        # العنوان
        pdf.set_font('DejaVu', '', 16)
        try:
            pdf.cell(0, 10, f'تقرير الطالب: {str(student_name)}', 0, 1, 'C')
        except:
            pdf.cell(0, 10, 'تقرير الطالب', 0, 1, 'C')
        pdf.ln(5)
        
        # إضافة معلومات التاريخ والوقت
        pdf.set_font('DejaVu', '', 10)
        current_date = datetime.now().strftime('%Y-%m-%d %H:%M')
        pdf.cell(0, 10, f'تاريخ التقرير: {current_date}', 0, 1, 'L')
        pdf.ln(5)
        
        # معلومات الدين
        pdf.set_font('DejaVu', '', 12)
        pdf.cell(0, 10, 'معلومات الدين', 0, 1, 'R')
        
        # استخراج البيانات مع التحقق من صحتها
        try:
            debt_amount = float(debt_data.get('debt_amount', 0))
        except (ValueError, TypeError):
            debt_amount = 0
            
        try:
            paid_amount = float(debt_data.get('paid_amount', 0))
        except (ValueError, TypeError):
            paid_amount = 0
            
        # حساب المبلغ المتبقي ونسبة السداد بطريقة آمنة
        remaining_amount = max(0, debt_amount - paid_amount)
        
        # تجنب القسمة على الصفر
        try:
            if debt_amount > 0:
                payment_percentage = (paid_amount / debt_amount) * 100
            else:
                payment_percentage = 0
        except:
            payment_percentage = 0
        
        # عرض البيانات
        pdf.set_font('DejaVu', '', 10)
        pdf.cell(70, 8, 'مبلغ الدين:', 0, 0, 'R')
        pdf.cell(0, 8, format_currency(debt_amount), 0, 1, 'R')
        
        pdf.cell(70, 8, 'المبلغ المسدد:', 0, 0, 'R')
        pdf.cell(0, 8, format_currency(paid_amount), 0, 1, 'R')
        
        pdf.cell(70, 8, 'المبلغ المتبقي:', 0, 0, 'R')
        pdf.cell(0, 8, format_currency(remaining_amount), 0, 1, 'R')
        
        pdf.cell(70, 8, 'حالة السداد:', 0, 0, 'R')
        
        # تحديد حالة السداد
        if payment_percentage >= 100:
            payment_status = "مسدد بالكامل"
        elif payment_percentage > 0:
            payment_status = "مسدد جزئياً"
        else:
            payment_status = "غير مسدد"
        
        pdf.cell(0, 8, payment_status, 0, 1, 'R')
        
        pdf.cell(70, 8, 'نسبة السداد:', 0, 0, 'R')
        try:
            percentage_text = f"{round(payment_percentage, 1)}%"
        except:
            percentage_text = "0%"
        pdf.cell(0, 8, percentage_text, 0, 1, 'R')
        
        # عرض سجل المعاملات إذا كان موجودًا وليس فارغًا
        if transactions_df is not None and not transactions_df.empty:
            pdf.ln(10)
            pdf.set_font('DejaVu', '', 12)
            pdf.cell(0, 10, 'سجل المعاملات', 0, 1, 'R')
            
            # التحقق من وجود الأعمدة المطلوبة
            required_columns = ['Transaction Type', 'Transaction Date', 'Amount', 'Notes']
            missing_columns = [col for col in required_columns if col not in transactions_df.columns]
            
            if missing_columns:
                pdf.set_font('DejaVu', '', 10)
                pdf.cell(0, 10, f"خطأ: بعض أعمدة البيانات مفقودة ({', '.join(missing_columns)})", 0, 1, 'C')
            else:
                # رؤوس الجدول
                pdf.set_font('DejaVu', '', 10)
                pdf.cell(50, 8, 'ملاحظات', 1, 0, 'C')
                pdf.cell(30, 8, 'المبلغ', 1, 0, 'C')
                pdf.cell(40, 8, 'نوع المعاملة', 1, 0, 'C')
                pdf.cell(40, 8, 'التاريخ', 1, 1, 'C')
                
                # بيانات المعاملات
                pdf.set_font('DejaVu', '', 10)
                for _, row in transactions_df.iterrows():
                    try:
                        # معالجة نوع المعاملة
                        transaction_type = 'دفع' if row.get('Transaction Type') == 'payment' else 'دين'
                        
                        # تنسيق التاريخ بطريقة آمنة
                        try:
                            if isinstance(row.get('Transaction Date'), str):
                                date_str = row.get('Transaction Date', '')
                            else:
                                date_obj = row.get('Transaction Date')
                                if date_obj:
                                    date_str = date_obj.strftime('%Y-%m-%d')
                                else:
                                    date_str = ''
                        except:
                            date_str = 'تاريخ غير صالح'
                        
                        # معالجة الملاحظات
                        notes = str(row.get('Notes', '')) if row.get('Notes') else ''
                        
                        # معالجة المبلغ
                        amount = row.get('Amount', 0)
                        
                        # طباعة الصف
                        pdf.cell(50, 8, notes[:30], 1, 0, 'R')  # الاقتصار على 30 حرف كحد أقصى للملاحظات
                        pdf.cell(30, 8, format_currency(amount), 1, 0, 'C')
                        pdf.cell(40, 8, transaction_type, 1, 0, 'C')
                        pdf.cell(40, 8, date_str, 1, 1, 'C')
                    except Exception as e:
                        # في حال حدوث خطأ في صف معين، نتخطاه
                        continue
        else:
            pdf.ln(10)
            pdf.set_font('DejaVu', '', 12)
            pdf.cell(0, 10, 'لا توجد معاملات مسجلة', 0, 1, 'C')
        
        # إضافة ملاحظة في نهاية التقرير
        pdf.ln(10)
        pdf.set_font('DejaVu', 'I', 8)
        pdf.cell(0, 10, 'المبالغ موضحة بالفلس الكويتي.', 0, 1, 'C')
        
        return pdf
    
    return create_pdf_buffer(create_content)

def export_payment_status_report_pdf(fully_paid_df, partially_paid_df, unpaid_df):
    """
    تصدير تقرير حالة السداد للطلاب إلى ملف PDF - نسخة محسنة مع تحقق أفضل من البيانات
    يعرض قوائم للطلاب المسددين بالكامل، المسددين جزئياً، وغير المسددين
    
    Args:
        fully_paid_df (DataFrame): إطار بيانات الطلاب المسددين بالكامل
        partially_paid_df (DataFrame): إطار بيانات الطلاب المسددين جزئياً
        unpaid_df (DataFrame): إطار بيانات الطلاب غير المسددين
        
    Returns:
        BytesIO: كائن ذاكرة يحتوي على ملف PDF
    """
    def create_content():
        # التحقق من البيانات - ضمان وجود إطارات البيانات
        if fully_paid_df is None:
            fully_paid_df = pd.DataFrame()
        if partially_paid_df is None:
            partially_paid_df = pd.DataFrame()
        if unpaid_df is None:
            unpaid_df = pd.DataFrame()
        
        # إنشاء كائن PDF جديد
        pdf = BasicPDF()
        
        # العنوان
        pdf.set_font('DejaVu', '', 16)
        pdf.cell(0, 10, 'تقرير حالة سداد الطلاب', 0, 1, 'C')
        pdf.ln(5)
        
        # إضافة معلومات التاريخ والوقت
        pdf.set_font('DejaVu', '', 10)
        current_date = datetime.now().strftime('%Y-%m-%d %H:%M')
        pdf.cell(0, 10, f'تاريخ التقرير: {current_date}', 0, 1, 'L')
        pdf.ln(5)
        
        # معلومات إجمالية
        pdf.set_font('DejaVu', '', 12)
        pdf.cell(0, 10, 'ملخص', 0, 1, 'R')
        
        pdf.set_font('DejaVu', '', 10)
        # تأكد من التعامل مع الأرقام على هيئة نصوص
        pdf.cell(70, 8, 'الطلاب المسددين بالكامل:', 0, 0, 'R')
        pdf.cell(0, 8, f"{len(fully_paid_df)}", 0, 1, 'R')
        
        pdf.cell(70, 8, 'الطلاب المسددين جزئياً:', 0, 0, 'R')
        pdf.cell(0, 8, f"{len(partially_paid_df)}", 0, 1, 'R')
        
        pdf.cell(70, 8, 'الطلاب غير المسددين:', 0, 0, 'R')
        pdf.cell(0, 8, f"{len(unpaid_df)}", 0, 1, 'R')
        
        total_students = len(fully_paid_df) + len(partially_paid_df) + len(unpaid_df)
        pdf.cell(70, 8, 'إجمالي عدد الطلاب:', 0, 0, 'R')
        pdf.cell(0, 8, f"{total_students}", 0, 1, 'R')
        
        # التحقق من وجود بيانات للعرض
        if total_students == 0:
            pdf.ln(10)
            pdf.set_font('DejaVu', '', 12)
            pdf.cell(0, 10, 'لا توجد بيانات للعرض', 0, 1, 'C')
            pdf.ln(5)
            pdf.set_font('DejaVu', '', 10)
            pdf.cell(0, 10, 'لم يتم العثور على أي سجلات للطلاب', 0, 1, 'C')
            return pdf
        
        # التحقق من وجود الأعمدة المطلوبة في كل إطار بيانات
        required_columns = ['Student Name', 'Debt Amount', 'Paid Amount', 'Remaining Amount']
        def check_dataframe_columns(df):
            if df.empty:
                return True  # لا داعي للتحقق من إطار بيانات فارغ
            missing = [col for col in required_columns if col not in df.columns]
            return len(missing) == 0
        
        # التحقق من إطارات البيانات
        all_valid = all([
            check_dataframe_columns(fully_paid_df), 
            check_dataframe_columns(partially_paid_df), 
            check_dataframe_columns(unpaid_df)
        ])
        
        if not all_valid:
            pdf.ln(10)
            pdf.set_font('DejaVu', '', 12)
            pdf.cell(0, 10, 'خطأ في هيكل البيانات', 0, 1, 'C')
            pdf.set_font('DejaVu', '', 10)
            pdf.cell(0, 10, 'بعض أعمدة البيانات المطلوبة غير موجودة', 0, 1, 'C')
            return pdf
        
        # الطلاب المسددين بالكامل
        if not fully_paid_df.empty:
            pdf.ln(10)
            pdf.set_font('DejaVu', '', 12)
            fully_paid_count = len(fully_paid_df)
            pdf.cell(0, 10, f'الطلاب المسددين بالكامل ({fully_paid_count})', 0, 1, 'R')
            
            # رؤوس الجدول
            pdf.set_font('DejaVu', '', 10)
            cell_width = 45
            pdf.cell(cell_width, 8, 'المبلغ المتبقي', 1, 0, 'C')
            pdf.cell(cell_width, 8, 'المبلغ المسدد', 1, 0, 'C')
            pdf.cell(cell_width, 8, 'مبلغ الدين', 1, 0, 'C')
            pdf.cell(cell_width, 8, 'اسم الطالب', 1, 1, 'C')
            
            # بيانات الجدول
            pdf.set_font('DejaVu', '', 10)
            for _, row in fully_paid_df.iterrows():
                try:
                    # التعامل مع الخلايا بطريقة آمنة
                    student_name = str(row.get('Student Name', 'غير معروف'))
                    debt_amount = row.get('Debt Amount', 0)
                    paid_amount = row.get('Paid Amount', 0)
                    remaining_amount = row.get('Remaining Amount', 0)
                    
                    # طباعة بيانات الطالب
                    pdf.cell(cell_width, 8, format_currency(remaining_amount), 1, 0, 'C')
                    pdf.cell(cell_width, 8, format_currency(paid_amount), 1, 0, 'C')
                    pdf.cell(cell_width, 8, format_currency(debt_amount), 1, 0, 'C')
                    pdf.cell(cell_width, 8, student_name, 1, 1, 'R')
                except Exception as e:
                    # في حال حدوث خطأ في معالجة هذا الصف، نتخطاه
                    continue
        
        # الطلاب المسددين جزئياً
        if not partially_paid_df.empty:
            pdf.ln(10)
            pdf.set_font('DejaVu', '', 12)
            partially_paid_count = len(partially_paid_df)
            pdf.cell(0, 10, f'الطلاب المسددين جزئياً ({partially_paid_count})', 0, 1, 'R')
            
            # رؤوس الجدول
            pdf.set_font('DejaVu', '', 10)
            pdf.cell(30, 8, 'نسبة السداد', 1, 0, 'C')
            pdf.cell(40, 8, 'المبلغ المتبقي', 1, 0, 'C')
            pdf.cell(40, 8, 'المبلغ المسدد', 1, 0, 'C')
            pdf.cell(40, 8, 'مبلغ الدين', 1, 0, 'C')
            pdf.cell(40, 8, 'اسم الطالب', 1, 1, 'C')
            
            # بيانات الجدول
            pdf.set_font('DejaVu', '', 10)
            for _, row in partially_paid_df.iterrows():
                try:
                    # التعامل مع الخلايا بطريقة آمنة
                    student_name = str(row.get('Student Name', 'غير معروف'))
                    debt_amount = row.get('Debt Amount', 0)
                    paid_amount = row.get('Paid Amount', 0)
                    remaining_amount = row.get('Remaining Amount', 0)
                    
                    # معالجة نسبة السداد
                    payment_percentage = 0
                    try:
                        if 'Payment Percentage' in row:
                            if isinstance(row['Payment Percentage'], (int, float)):
                                payment_percentage = row['Payment Percentage']
                            elif isinstance(row['Payment Percentage'], str):
                                # إزالة علامة % إذا كانت موجودة
                                clean_percent = row['Payment Percentage'].replace('%', '')
                                payment_percentage = float(clean_percent)
                        else:
                            # حساب النسبة المئوية إذا لم تكن موجودة
                            if debt_amount > 0:
                                payment_percentage = (paid_amount / debt_amount) * 100
                    except (ValueError, TypeError):
                        payment_percentage = 0
                    
                    # تنسيق النسبة المئوية
                    percentage_text = f"{round(payment_percentage, 1)}%"
                    
                    # طباعة بيانات الطالب
                    pdf.cell(30, 8, percentage_text, 1, 0, 'C')
                    pdf.cell(40, 8, format_currency(remaining_amount), 1, 0, 'C')
                    pdf.cell(40, 8, format_currency(paid_amount), 1, 0, 'C')
                    pdf.cell(40, 8, format_currency(debt_amount), 1, 0, 'C')
                    pdf.cell(40, 8, student_name, 1, 1, 'R')
                except Exception as e:
                    # في حال حدوث خطأ في معالجة هذا الصف، نتخطاه
                    continue
        
        # الطلاب غير المسددين
        if not unpaid_df.empty:
            pdf.ln(10)
            pdf.set_font('DejaVu', '', 12)
            unpaid_count = len(unpaid_df)
            pdf.cell(0, 10, f'الطلاب غير المسددين ({unpaid_count})', 0, 1, 'R')
            
            # رؤوس الجدول
            pdf.set_font('DejaVu', '', 10)
            cell_width = 45
            pdf.cell(cell_width, 8, 'المبلغ المتبقي', 1, 0, 'C')
            pdf.cell(cell_width, 8, 'المبلغ المسدد', 1, 0, 'C')
            pdf.cell(cell_width, 8, 'مبلغ الدين', 1, 0, 'C')
            pdf.cell(cell_width, 8, 'اسم الطالب', 1, 1, 'C')
            
            # بيانات الجدول
            pdf.set_font('DejaVu', '', 10)
            for _, row in unpaid_df.iterrows():
                try:
                    # التعامل مع الخلايا بطريقة آمنة
                    student_name = str(row.get('Student Name', 'غير معروف'))
                    debt_amount = row.get('Debt Amount', 0)
                    paid_amount = row.get('Paid Amount', 0)
                    remaining_amount = row.get('Remaining Amount', 0)
                    
                    # طباعة بيانات الطالب
                    pdf.cell(cell_width, 8, format_currency(remaining_amount), 1, 0, 'C')
                    pdf.cell(cell_width, 8, format_currency(paid_amount), 1, 0, 'C')
                    pdf.cell(cell_width, 8, format_currency(debt_amount), 1, 0, 'C')
                    pdf.cell(cell_width, 8, student_name, 1, 1, 'R')
                except Exception as e:
                    # في حال حدوث خطأ في معالجة هذا الصف، نتخطاه
                    continue
        
        # إضافة ملاحظة في نهاية التقرير
        pdf.ln(10)
        pdf.set_font('DejaVu', 'I', 8)
        pdf.cell(0, 10, 'المبالغ موضحة بالفلس الكويتي.', 0, 1, 'C')
        
        return pdf
    
    return create_pdf_buffer(create_content)

def create_download_link(pdf_bytes, filename):
    """إنشاء رابط تنزيل للملف"""
    if pdf_bytes is None:
        return "فشل إنشاء الملف"
    
    b64 = base64.b64encode(pdf_bytes.read()).decode()
    href = f'<a href="data:application/pdf;base64,{b64}" download="{filename}" class="download-link">تنزيل الملف</a>'
    return href