import streamlit as st
import pandas as pd
import os
import datetime
import io
import base64
import plotly.express as px
import plotly.graph_objects as go
from database import DatabaseManager
from visualization import create_debt_visualization, create_debt_distribution_chart
from utils import validate_student_name, validate_debt_amount, format_currency, generate_excel_template
# استيراد وظائف التقرير البسيط (احتياطي)
from simple_report import create_all_students_report as create_all_students_report_simple
from simple_report import create_student_report as create_student_report_simple
from simple_report import create_payment_status_report as create_payment_status_report_simple
from simple_report import create_download_link

# استيراد وظائف التقرير المحسن
from enhanced_report import create_enhanced_all_students_report, create_enhanced_student_report, create_enhanced_payment_status_report

# تعيين وظائف التقرير الافتراضية
create_all_students_report = create_enhanced_all_students_report
create_student_report = create_enhanced_student_report
create_payment_status_report = create_enhanced_payment_status_report

# إعدادات الصفحة
st.set_page_config(
    page_title="نظام إدارة ديون الطلاب",
    page_icon="📚",
    layout="wide"
)

# تطبيق تنسيق CSS مخصص
st.markdown("""
<style>
    /* تنسيق عام */
    .main .block-container {
        padding-top: 2rem;
        padding-bottom: 2rem;
    }
    h1, h2, h3, h4, h5, h6 {
        color: #0078D4;
        margin-bottom: 1rem;
    }
    /* تنسيق البطاقات والأقسام */
    .dashboard-card {
        background-color: white;
        border-radius: 10px;
        padding: 1.5rem;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        margin-bottom: 1rem;
    }
    /* تنسيق الجداول */
    .dataframe {
        width: 100%;
        border-collapse: collapse;
    }
    .dataframe th {
        background-color: #f0f2f6;
        padding: 10px;
        text-align: center;
        font-weight: bold;
        border: 1px solid #ddd;
    }
    .dataframe td {
        padding: 8px;
        border: 1px solid #ddd;
        text-align: center;
    }
    .dataframe tr:hover {
        background-color: #f5f5f5;
    }
    /* تنسيق الأزرار */
    .stButton>button {
        font-weight: bold;
        border-radius: 6px;
        padding: 0.5rem 1rem;
        transition: all 0.2s;
    }
    .stButton>button:hover {
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        transform: translateY(-2px);
    }
    /* تنسيق الشريط الجانبي */
    .css-1d391kg {
        padding-top: 2rem;
    }
    .sidebar-student-button {
        margin-bottom: 5px;
        text-align: right;
        width: 100%;
    }
    /* تنسيق الفواصل */
    hr {
        margin: 2rem 0;
        border: none;
        height: 1px;
        background-color: #ddd;
    }
    /* لتحسين مظهر المقاييس */
    .stMetric {
        background-color: white;
        border-radius: 8px;
        padding: 10px;
        box-shadow: 0 1px 2px rgba(0,0,0,0.05);
    }
    /* لتحسين مظهر نماذج الإدخال */
    .stForm {
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 20px;
    }
    /* تنسيق للشاشات الصغيرة */
    @media (max-width: 768px) {
        .stForm {
            padding: 10px;
        }
    }
</style>
""", unsafe_allow_html=True)

# تهيئة متغيرات حالة الجلسة إذا لم تكن موجودة
if 'debt_manager' not in st.session_state:
    st.session_state.debt_manager = DatabaseManager()

# تهيئة متغير حالة لمعالجة إدخال البيانات بمجرد الضغط على Enter
if 'process_on_next_render' not in st.session_state:
    st.session_state.process_on_next_render = False

# العنوان الرئيسي
st.title("نظام إدارة ديون الطلاب")

# الشريط الجانبي للتنقل
st.sidebar.title("القائمة")

# صفحات التنقل الرئيسية
page = st.sidebar.radio("اذهب إلى", ["إضافة/تحديث الديون", "عرض جميع الديون", "التقارير", "استيراد/تصدير"])

# التحقق مما إذا كان يجب معالجة البيانات (إذا تم الضغط على Enter في الشاشة السابقة)
if st.session_state.process_on_next_render:
    # إعادة تعيين متغير الحالة لمنع المعالجة المتكررة
    st.session_state.process_on_next_render = False
    
    # تنفيذ معالجة النموذج فقط إذا كنا في صفحة إضافة/تحديث الديون
    if page == "إضافة/تحديث الديون":
        # التحقق من وجود البيانات المطلوبة
        if 'student_name' in st.session_state and 'debt_amount' in st.session_state:
            try:
                student_name = st.session_state.student_name
                debt_amount = st.session_state.debt_amount
                paid_amount = st.session_state.paid_amount if 'paid_amount' in st.session_state else "0"
                
                # التحقق من صحة البيانات
                name_valid, name_error = validate_student_name(student_name)
                amount_valid, amount_error = validate_debt_amount(debt_amount)
                paid_valid, paid_error = validate_debt_amount(paid_amount) if paid_amount else (True, "")
                
                if name_valid and amount_valid and paid_valid:
                    amount = float(debt_amount)
                    paid = float(paid_amount) if paid_amount else 0.0
                    
                    # التحقق مما إذا كان الطالب موجودًا بالفعل
                    if student_name in st.session_state.debt_manager.get_all_students():
                        # تحديد طريقة التحديث (للطلاب الموجودين)
                        is_debt_additional = ('debt_operation_type' in st.session_state and 
                                            st.session_state.debt_operation_type == "إضافة مبلغ للدين الحالي")
                        is_paid_additional = ('paid_operation_type' in st.session_state and 
                                             st.session_state.paid_operation_type == "إضافة مبلغ للمسدد الحالي")
                        
                        # إضافة ملاحظات للمعاملات
                        debt_notes = f"تحديث يدوي - Enter - {datetime.datetime.now().strftime('%Y-%m-%d')}"
                        payment_notes = f"تحديث يدوي - Enter - {datetime.datetime.now().strftime('%Y-%m-%d')}"
                        
                        # تحديث الدين والمبلغ المسدد
                        debt_success = st.session_state.debt_manager.update_debt(
                            student_name, amount, is_additional=is_debt_additional, notes=debt_notes)
                        paid_success = st.session_state.debt_manager.update_paid_amount(
                            student_name, paid, is_additional=is_paid_additional, notes=payment_notes)
                        
                    else:
                        # إضافة طالب جديد
                        add_notes = f"إضافة طالب جديد - Enter - {datetime.datetime.now().strftime('%Y-%m-%d')}"
                        payment_notes = f"تسديد أولي - Enter - {datetime.datetime.now().strftime('%Y-%m-%d')}"
                        
                        if st.session_state.debt_manager.add_debt(student_name, amount, notes=add_notes):
                            if paid > 0:
                                st.session_state.debt_manager.update_paid_amount(student_name, paid, notes=payment_notes)
                    
                    # إعادة تعيين النموذج
                    st.session_state.student_name = ""
                    st.session_state.debt_amount = ""
                    st.session_state.paid_amount = "0"
                    # نعيد تمكين الإدخال بعد نجاح العملية
                    st.session_state.enable_enter_key = True
                    
            except Exception as e:
                st.error(f"حدث خطأ أثناء معالجة النموذج: {str(e)}")

# إضافة ملخص لإجمالي المديونيات في الأعلى
if not st.session_state.debt_manager.is_empty():
    # الحصول على جميع سجلات الديون
    df = st.session_state.debt_manager.get_dataframe()
    
    # عرض إجمالي الدين والمبالغ المسددة والمتبقية
    total_debt = df['Debt Amount'].sum()
    total_paid = df['Paid Amount'].sum()
    total_remaining = df['Remaining Amount'].sum()
    
    # عرض الإجماليات في أعلى الصفحة
    col1, col2, col3, col4 = st.columns([1, 1, 1, 1])
    col1.metric("إجمالي عدد الطلاب", f"{len(df)}")
    col2.metric("إجمالي الديون", format_currency(total_debt))
    col3.metric("إجمالي المسدد", format_currency(total_paid))
    col4.metric("إجمالي المتبقي", format_currency(total_remaining))
    
    # إضافة خط فاصل
    st.markdown("---")

# إضافة قائمة الطلاب للوصول السريع
with st.sidebar:
    # إضافة قائمة بأسماء الطلاب للتنقل السريع إذا كانت هناك سجلات
    if not st.session_state.debt_manager.is_empty():
        st.markdown("---")
        st.markdown("### الطلاب:")
        
        # الحصول على قائمة أسماء الطلاب
        student_names = st.session_state.debt_manager.get_all_students()
        
        # تخزين الطالب المحدد في حالة الجلسة
        if 'selected_student_from_sidebar' not in st.session_state:
            st.session_state['selected_student_from_sidebar'] = None
            
        # عرض قائمة الطلاب كأزرار
        for name in student_names:
            if st.button(name, key=f"sidebar_student_{name}"):
                # تخزين اسم الطالب المحدد
                st.session_state['selected_student_from_sidebar'] = name
                # التأكد من أن الصفحة النشطة هي صفحة الإضافة/التحديث
                if page != "إضافة/تحديث الديون":
                    page = "إضافة/تحديث الديون"
                # الانتقال إلى صفحة الطالب مع تعيين الحالة المطلوبة
                st.session_state["selected_student_info"] = {
                    "name": name,
                    "debt_amount": st.session_state.debt_manager.get_debt(name),
                    "paid_amount": st.session_state.debt_manager.get_paid_amount(name) or 0
                }
                st.rerun()

# إضافة خيار للتنزيل السريع لملف Excel الفارغ
with st.sidebar:
    st.markdown("---")
    st.markdown("### تنزيل سريع:")
    
    # زر لتحميل قالب فارغ
    if st.button("تحميل قالب Excel فارغ", key="sidebar_download"):
        # إنشاء قالب فارغ
        excel_template = generate_excel_template()
        # إنشاء رابط تنزيل
        st.download_button(
            label="تنزيل القالب الآن",
            data=excel_template,
            file_name="نظام_إدارة_ديون_الطلاب.xlsx",
            mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            key="sidebar_download_button"
        )

# وظيفة لإعادة تعيين قيم النموذج ومتغيرات الحالة
def reset_form():
    # تنظيف جميع متغيرات الحالة ذات الصلة
    if "selected_student_info" in st.session_state:
        del st.session_state["selected_student_info"]
    
    if "selected_student_from_sidebar" in st.session_state:
        st.session_state['selected_student_from_sidebar'] = None
    
    if "similar_names" in st.session_state:
        st.session_state.similar_names = []
    
    if "search_results" in st.session_state:
        st.session_state.search_results = False

if page == "إضافة/تحديث الديون":
    st.header("إضافة أو تحديث دين طالب")
    
    # تهيئة متغيرات للبحث عن الاسم
    if 'similar_names' not in st.session_state:
        st.session_state.similar_names = []
    
    # إعداد متغير لتخزين نتيجة البحث
    if 'search_results' not in st.session_state:
        st.session_state.search_results = False
    
    # حقول الإدخال خارج النموذج للبحث الفوري
    st.markdown('<div class="dashboard-card">', unsafe_allow_html=True)
    st.subheader("🔍 بحث عن اسم الطالب")
    col1, col2 = st.columns([3, 1])
    
    with col1:
        student_name_search = st.text_input("ابدأ بكتابة اسم الطالب", key="student_name_search")
        
    with col2:
        st.write("&nbsp;")  # مسافة فارغة للمحاذاة
        search_button = st.button("🔍 بحث", type="primary")
    st.markdown('</div>', unsafe_allow_html=True)
    
    # البحث عن الأسماء المشابهة
    if (student_name_search and len(student_name_search) >= 2) or search_button:
        # عند البحث، حفظ النتائج في متغير حالة منفصل
        search_results = st.session_state.debt_manager.find_similar_names(student_name_search)
        if search_results != st.session_state.similar_names:
            st.session_state.similar_names = search_results
            st.session_state.search_results = True
        
    # عرض نتائج البحث والسماح باختيار طالب
    selected_student = None
    if st.session_state.similar_names:
        st.write("الطلاب المطابقين للبحث:")
        for name in st.session_state.similar_names:
            button_key = f"select_student_{name}"
            if st.button(f"اختر: {name}", key=button_key):
                # تخزين معلومات الطالب في حالة الجلسة
                st.session_state["selected_student_info"] = {
                    "name": name,
                    "debt_amount": st.session_state.debt_manager.get_debt(name),
                    "paid_amount": st.session_state.debt_manager.get_paid_amount(name) or 0
                }
                st.rerun()
    
    # عرض معلومات الطالب المحدد إذا كانت موجودة
    if "selected_student_info" in st.session_state:
        info = st.session_state["selected_student_info"]
        
        # بطاقة معلومات الطالب
        st.markdown('<div class="dashboard-card">', unsafe_allow_html=True)
        st.subheader(f"👤 معلومات الطالب: {info['name']}")
        
        # احتساب نسبة السداد
        payment_percentage = 0
        if info['debt_amount'] > 0:
            payment_percentage = (info['paid_amount'] / info['debt_amount']) * 100
            
        # تحديد حالة السداد ولونها
        payment_status = "غير مسدد"
        status_color = "#f44336" # أحمر
        if payment_percentage >= 100:
            payment_status = "مسدد بالكامل"
            status_color = "#4caf50" # أخضر
        elif payment_percentage > 0:
            payment_status = "مسدد جزئياً"
            status_color = "#ff9800" # برتقالي
            
        # عرض المعلومات الأساسية في صفوف
        col1, col2, col3, col4 = st.columns(4)
        col1.metric("قيمة الدين", format_currency(info['debt_amount']))
        col2.metric("المبلغ المسدد", format_currency(info['paid_amount']))
        col3.metric("المتبقي", format_currency(info['debt_amount'] - info['paid_amount']))
        col4.metric("نسبة السداد", f"{payment_percentage:.1f}%")
        
        # عرض حالة السداد بلون خاص
        st.markdown(f"""
        <div style="text-align: center; margin: 20px 0; padding: 10px; background-color: {status_color}; color: white; border-radius: 5px; font-weight: bold;">
            حالة السداد: {payment_status}
        </div>
        """, unsafe_allow_html=True)
        
        st.markdown('</div>', unsafe_allow_html=True)
        
        # عرض سجل المعاملات للطالب
        st.markdown('<div class="dashboard-card">', unsafe_allow_html=True)
        st.subheader(f"📋 سجل معاملات الطالب: {info['name']}")
        transactions_df = st.session_state.debt_manager.get_student_transactions(info['name'])
        
        if not transactions_df.empty:
            # تنسيق العملة بالفلس مباشرة
            transactions_df['Formatted Amount'] = transactions_df['Amount'].apply(lambda x: format_currency(x))
            
            # تنسيق التاريخ في عمود التاريخ
            transactions_df['Transaction Date'] = transactions_df['Transaction Date'].apply(
                lambda x: x.strftime("%Y-%m-%d %H:%M:%S")
            )
            
            # إعداد العمود الذي سيتم عرضه (استبدال عمود المبلغ الأصلي بالمبلغ المنسق)
            display_df = transactions_df[['Transaction Type', 'Formatted Amount', 'Transaction Date', 'Notes']].rename(
                columns={'Formatted Amount': 'Amount'}
            )
            
            # عرض جدول المعاملات
            st.table(display_df)
        else:
            st.info("لا توجد معاملات مسجلة لهذا الطالب.")
        
        # أزرار التحكم
        col1, col2, col3 = st.columns(3)
        
        # زر إلغاء الاختيار
        if col1.button("إلغاء اختيار الطالب"):
            del st.session_state["selected_student_info"]
            st.rerun()
        
        # زر حذف الطالب
        if col2.button("حذف الطالب", type="primary", help="حذف هذا الطالب وجميع بياناته"):
            if st.session_state.debt_manager.delete_debt(info['name']):
                st.success(f"تم حذف الطالب {info['name']} بنجاح")
                del st.session_state["selected_student_info"]
                st.rerun()
            else:
                st.error(f"فشل حذف الطالب {info['name']}")
                
        # زر تصدير تقرير PDF للطالب
        if col3.button("📄 تصدير PDF", help="تصدير تقرير مفصل للطالب بصيغة PDF"):
            try:
                # إنشاء ملف PDF باستخدام المكتبة الجديدة
                pdf_bytes = create_student_report(
                    info['name'],
                    {
                        'debt_amount': info['debt_amount'],
                        'paid_amount': info['paid_amount'],
                    },
                    transactions_df
                )
                
                # التحقق من وجود الملف قبل عرض زر التنزيل
                if pdf_bytes:
                    # توفير رابط التنزيل
                    st.download_button(
                        label="تنزيل تقرير الطالب PDF",
                        data=pdf_bytes,
                        file_name=f"student_report_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
                        mime="application/pdf"
                    )
                    st.success("تم إنشاء تقرير PDF بنجاح!")
                else:
                    # لن يحدث هذا لأن الدالة محسنة لإرجاع بيانات صالحة دائمًا
                    st.error("فشل إنشاء ملف PDF. لا توجد بيانات مرجعة.")
            except Exception as e:
                st.error(f"فشل إنشاء تقرير PDF: {str(e)}")
                # إضافة مزيد من المعلومات للتصحيح
                st.error(f"نوع الخطأ: {type(e).__name__}")
        
        # إضافة زر تسديد المديونية بالكامل
        col1, col2 = st.columns(2)
        remaining = info['debt_amount'] - info['paid_amount']
        if remaining > 0:
            # عرض المبلغ المتبقي وزر التسديد الكامل
            if col1.button(f"💰 تسديد المديونية بالكامل ({format_currency(remaining)})", type="primary"):
                payment_notes = f"تسديد كامل المديونية - {datetime.datetime.now().strftime('%Y-%m-%d')}"
                if st.session_state.debt_manager.update_paid_amount(
                    info['name'], 
                    info['debt_amount'],  # تعيين المبلغ المدفوع ليكون مساوياً للدين
                    is_additional=False,  # استبدال القيمة الحالية
                    notes=payment_notes
                ):
                    st.success(f"تم تسديد كامل مديونية الطالب {info['name']} بنجاح!")
                    # تحديث معلومات الطالب في حالة الجلسة
                    st.session_state["selected_student_info"]["paid_amount"] = info['debt_amount']
                    st.rerun()
                else:
                    st.error(f"حدث خطأ أثناء تسديد المديونية للطالب {info['name']}")
        else:
            col1.success(f"تمت تسديد كامل المديونية للطالب {info['name']}")
            col2.info("لا توجد مبالغ متبقية للتسديد")
    
    # نموذج إضافة/تحديث الدين
    st.markdown('<div class="dashboard-card">', unsafe_allow_html=True)
    st.subheader("💾 نموذج إضافة/تحديث الدين")
    
    # تهيئة متغير حالة لمعالجة الضغط على Enter
    if 'enable_enter_key' not in st.session_state:
        st.session_state.enable_enter_key = True
    
    # دالة للمعالجة عند الضغط على Enter
    def handle_enter_key():
        if st.session_state.enable_enter_key:
            # تعطيل مؤقتاً لمنع التنفيذ المتكرر
            st.session_state.enable_enter_key = False
            # تعيين متغير حالة لتنفيذ العملية في المرة القادمة عند إعادة تشغيل التطبيق
            st.session_state.process_on_next_render = True
    
    # تحديد القيم الأولية للحقول
    initial_name = ""
    initial_debt = ""
    initial_paid = "0"
    is_existing_student = False
    
    # استخدام معلومات الطالب المحدد إذا كانت موجودة
    if "selected_student_info" in st.session_state:
        info = st.session_state["selected_student_info"]
        initial_name = info["name"]
        initial_debt = str(info["debt_amount"])
        initial_paid = str(info["paid_amount"])
        is_existing_student = True
    
    # تنبيه بحالة التحديث
    if is_existing_student:
        st.info(f"أنت تقوم بتحديث بيانات الطالب: {initial_name}")
    
    # دالة معالجة البيانات عند الضغط على Enter أو الحفظ
    def process_form_data():
        # التحقق من صحة بيانات الإدخال
        student_name = st.session_state.student_name
        debt_amount = st.session_state.debt_amount
        paid_amount = st.session_state.paid_amount
        
        # تحديد طريقة التحديث للطلاب الموجودين
        if is_existing_student:
            debt_operation = st.session_state.debt_operation_type
            paid_operation = st.session_state.paid_operation_type
        else:
            debt_operation = "تعيين قيمة جديدة"
            paid_operation = "تعيين قيمة جديدة"
            
        name_valid, name_error = validate_student_name(student_name)
        amount_valid, amount_error = validate_debt_amount(debt_amount)
        paid_valid, paid_error = validate_debt_amount(paid_amount) if paid_amount else (True, "")
        
        if not name_valid:
            st.error(name_error)
            return False
        elif not amount_valid:
            st.error(amount_error)
            return False
        elif not paid_valid:
            st.error(paid_error)
            return False
        
        # إضافة أو تحديث سجل الدين
        try:
            amount = float(debt_amount)
            paid = float(paid_amount) if paid_amount else 0.0
            
            # التحقق مما إذا كان الطالب موجودًا بالفعل
            if student_name in st.session_state.debt_manager.get_all_students():
                # تحديد طريقة التحديث
                is_debt_additional = (debt_operation == "إضافة مبلغ للدين الحالي")
                is_paid_additional = (paid_operation == "إضافة مبلغ للمسدد الحالي")
                
                # إضافة ملاحظات للمعاملات
                debt_notes = f"تحديث يدوي من النظام - {datetime.datetime.now().strftime('%Y-%m-%d')}"
                payment_notes = f"تحديث يدوي من النظام - {datetime.datetime.now().strftime('%Y-%m-%d')}"
                
                # محاولة تحديث الدين
                debt_success = st.session_state.debt_manager.update_debt(
                    student_name, 
                    amount, 
                    is_additional=is_debt_additional,
                    notes=debt_notes
                )
                
                if not debt_success:
                    st.error(f"فشل تحديث دين {student_name}")
                
                # محاولة تحديث المبلغ المسدد
                paid_success = st.session_state.debt_manager.update_paid_amount(
                    student_name, 
                    paid, 
                    is_additional=is_paid_additional,
                    notes=payment_notes
                )
                
                if not paid_success:
                    st.error(f"فشل تحديث المبلغ المسدد لـ {student_name}")
                
                # إذا نجحت عملية واحدة على الأقل، نعرض رسالة نجاح
                if debt_success or paid_success:
                    if debt_success and paid_success:
                        st.success(f"تم تحديث بيانات {student_name} بنجاح!")
                    elif debt_success:
                        st.success(f"تم تحديث دين {student_name} بنجاح!")
                    elif paid_success:
                        st.success(f"تم تحديث المبلغ المسدد لـ {student_name} بنجاح!")
            else:
                # إضافة طالب جديد
                add_notes = f"إضافة طالب جديد - {datetime.datetime.now().strftime('%Y-%m-%d')}"
                payment_notes = f"تسديد أولي - {datetime.datetime.now().strftime('%Y-%m-%d')}"
                
                if st.session_state.debt_manager.add_debt(student_name, amount, notes=add_notes):
                    if paid > 0:
                        st.session_state.debt_manager.update_paid_amount(student_name, paid, notes=payment_notes)
                    st.success(f"تمت إضافة {student_name} بنجاح!")
                else:
                    st.error(f"فشلت إضافة {student_name}")
            
            # إعادة تعيين النموذج وقائمة البحث
            reset_form()
            st.session_state.similar_names = []
            st.rerun()
            return True
        except Exception as e:
            st.error(f"حدث خطأ: {str(e)}")
            return False
            
    # حقول الإدخال مع دعم الضغط على Enter
    student_name = st.text_input("👤 اسم الطالب", value=initial_name, key="student_name", on_change=handle_enter_key)
    
    # خيارات للطلاب الموجودين
    if is_existing_student:
        # خيارات لطريقة إدخال الدين
        debt_operation = st.radio(
            "طريقة تحديث الدين",
            ["تعيين قيمة جديدة", "إضافة مبلغ للدين الحالي"],
            index=0,
            horizontal=True,
            key="debt_operation_type"
        )
        debt_label = "المبلغ المراد إضافته للدين" if debt_operation == "إضافة مبلغ للدين الحالي" else "قيمة الدين"
        debt_amount = st.text_input(debt_label, value="" if debt_operation == "إضافة مبلغ للدين الحالي" else initial_debt, key="debt_amount", on_change=handle_enter_key)
        
        # خيارات لطريقة إدخال المبلغ المسدد
        paid_operation = st.radio(
            "طريقة تحديث المبلغ المسدد",
            ["تعيين قيمة جديدة", "إضافة مبلغ للمسدد الحالي"],
            index=0,
            horizontal=True,
            key="paid_operation_type"
        )
        paid_label = "المبلغ المراد إضافته للمسدد" if paid_operation == "إضافة مبلغ للمسدد الحالي" else "المبلغ المسدد"
        paid_amount = st.text_input(paid_label, value="" if paid_operation == "إضافة مبلغ للمسدد الحالي" else initial_paid, key="paid_amount", on_change=handle_enter_key)
    else:
        # للطلاب الجدد، الخيار الوحيد هو تعيين قيم جديدة
        debt_operation = "تعيين قيمة جديدة"
        paid_operation = "تعيين قيمة جديدة"
        debt_amount = st.text_input("💰 قيمة الدين", value=initial_debt, key="debt_amount", on_change=handle_enter_key)
        paid_amount = st.text_input("💵 المبلغ المسدد", value=initial_paid, key="paid_amount", on_change=handle_enter_key)
        
        # أزرار التحكم (استخدام زر عادي بدلاً من زر النموذج)
        col1, col2 = st.columns(2)
        if col1.button(label="💾 حفظ", type="primary", key="manual_submit"):
            process_form_data()
        if col2.button(label="🔄 إعادة تعيين", key="manual_reset"):
            reset_form()
            st.rerun()
    
    # تم استبدال المعالجة القديمة بدالة process_form_data التي تُنفذ عند الضغط على مفتاح Enter أو زر الحفظ
    
    # عرض سريع للإضافات/التحديثات الأخيرة
    st.markdown('</div>', unsafe_allow_html=True)  # إغلاق بطاقة النموذج
    
    if not st.session_state.debt_manager.is_empty():
        st.markdown('<div class="dashboard-card">', unsafe_allow_html=True)
        st.subheader("🔄 سجلات الديون الأخيرة")
        df = st.session_state.debt_manager.get_dataframe().tail(5)
        
        # تنسيق العملة في الجدول
        formatted_df = df.copy()
        for col in ['Debt Amount', 'Paid Amount', 'Remaining Amount']:
            formatted_df[col] = formatted_df[col].apply(lambda x: format_currency(x))
            
        st.table(formatted_df)
        st.markdown('</div>', unsafe_allow_html=True)

elif page == "عرض جميع الديون":
    st.header("جميع ديون الطلاب")
    
    if st.session_state.debt_manager.is_empty():
        st.info("لم يتم العثور على سجلات ديون. أضف بعض السجلات أولاً.")
    else:
        # الحصول على جميع سجلات الديون
        df = st.session_state.debt_manager.get_dataframe()
        
        # بطاقة الإحصائيات
        st.markdown('<div class="dashboard-card">', unsafe_allow_html=True)
        st.subheader("📊 إحصائيات الديون")
        
        # عرض إجمالي الدين والمبالغ المسددة والمتبقية
        total_debt = df['Debt Amount'].sum()
        total_paid = df['Paid Amount'].sum()
        total_remaining = df['Remaining Amount'].sum()
        
        # إضافة أزرار الإجراءات
        col1, col2, col3, col4 = st.columns([1, 1, 1, 1])
        col1.metric("إجمالي الديون", format_currency(total_debt))
        col2.metric("إجمالي المسدد", format_currency(total_paid))
        col3.metric("إجمالي المتبقي", format_currency(total_remaining))
        col4.metric("عدد الطلاب", f"{len(df)}")
        
        # عرض نسبة التحصيل
        payment_percentage = 0
        if total_debt > 0:
            payment_percentage = (total_paid / total_debt) * 100
        
        # عرض شريط التقدم للتحصيل
        st.markdown(f"""
        <div style="margin: 20px 0;">
            <p style="margin-bottom: 5px; text-align: center;">نسبة التحصيل: {payment_percentage:.1f}%</p>
            <div style="width: 100%; background-color: #e0e0e0; border-radius: 5px;">
                <div style="width: {min(payment_percentage, 100)}%; background-color: {'#4caf50' if payment_percentage >= 75 else '#ff9800' if payment_percentage >= 30 else '#f44336'}; 
                    height: 25px; border-radius: 5px; text-align: center; line-height: 25px; color: white; font-weight: bold;">
                    {payment_percentage:.1f}%
                </div>
            </div>
        </div>
        """, unsafe_allow_html=True)
        
        st.markdown('</div>', unsafe_allow_html=True)
        
        # بطاقة إجراءات التقارير
        st.markdown('<div class="dashboard-card">', unsafe_allow_html=True)
        st.subheader("🖨️ طباعة وتصدير البيانات")
        
        col1, col2 = st.columns(2)
        # إضافة زر طباعة التقرير
        if col1.button("🖨️ طباعة التقرير", help="طباعة تقرير الديون", type="primary"):
            st.markdown('</div>', unsafe_allow_html=True)
            try:
                # إنشاء ملف PDF باستخدام المكتبة الجديدة البسيطة
                pdf_bytes = create_all_students_report(df)
                
                # التحقق من وجود الملف قبل المعالجة
                if pdf_bytes:
                    # فتح التقرير في نافذة جديدة للطباعة
                    try:
                        # التعامل مع PDF كبيانات ثنائية مباشرة
                        pdf_bytes_copy = pdf_bytes
                        if hasattr(pdf_bytes, 'read'):
                            pdf_bytes_copy = pdf_bytes.read()
                        
                        # ترميز البيانات
                        b64_pdf = base64.b64encode(pdf_bytes_copy).decode()
                        pdf_display = f'<iframe src="data:application/pdf;base64,{b64_pdf}" width="100%" height="600" type="application/pdf"></iframe>'
                        
                        # عرض واجهة الطباعة
                        st.markdown("### طباعة التقرير")
                        st.markdown(pdf_display, unsafe_allow_html=True)
                    except Exception as e:
                        st.error(f"خطأ في معالجة ملف PDF: {str(e)}")
                        st.error(f"نوع البيانات: {type(pdf_bytes)}")
                else:
                    st.error("فشل إنشاء ملف PDF. لا توجد بيانات مرجعة.")
                
                # إضافة زر الطباعة الفعلي باستخدام JavaScript
                js_print = """
                <script>
                    function printPDF() {
                        window.frames[0].focus();
                        window.frames[0].print();
                    }
                </script>
                <button onclick="printPDF();" class="button">🖨️ اطبع الآن</button>
                """
                st.markdown(js_print, unsafe_allow_html=True)
                
                # إضافة زر التنزيل أيضاً
                if pdf_bytes:
                    try:
                        # نسخة أخرى من البيانات للتنزيل
                        # إذا كان ملف كائن له خاصية seek، نستخدمها
                        if hasattr(pdf_bytes, 'seek'):
                            pdf_bytes.seek(0)
                            download_bytes = pdf_bytes
                        else:
                            # إذا كانت بيانات ثنائية مباشرة، نستخدمها كما هي
                            download_bytes = pdf_bytes
                            
                        st.download_button(
                            label="تنزيل تقرير PDF",
                            data=download_bytes,
                            file_name=f"all_students_report_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
                            mime="application/pdf"
                        )
                    except Exception as e:
                        st.error(f"خطأ أثناء التنزيل: {str(e)}")
                        st.error(f"نوع البيانات: {type(pdf_bytes)}")
            except Exception as e:
                st.error(f"فشل إنشاء تقرير PDF للطباعة: {str(e)}")
        
        # إغلاق بطاقة طباعة وتصدير البيانات
        st.markdown('</div>', unsafe_allow_html=True)
        
        # بطاقة عرض البيانات والبحث
        st.markdown('<div class="dashboard-card">', unsafe_allow_html=True)
        st.subheader("🔍 بيانات الطلاب")
        
        # وظيفة البحث
        search_term = st.text_input("البحث حسب اسم الطالب")
        
        # تنسيق العملة في الجدول
        formatted_df = df.copy()
        for col in ['Debt Amount', 'Paid Amount', 'Remaining Amount']:
            formatted_df[col] = formatted_df[col].apply(lambda x: format_currency(x))
        
        # تنسيق العرض
        if search_term:
            st.success(f"نتائج البحث عن: {search_term}")
            filtered_df = formatted_df[formatted_df['Student Name'].str.contains(search_term, case=False)]
            st.dataframe(filtered_df, use_container_width=True)
        else:
            st.dataframe(formatted_df, use_container_width=True)
            
        st.markdown('</div>', unsafe_allow_html=True)
        
        # بطاقة تصور الديون
        st.markdown('<div class="dashboard-card">', unsafe_allow_html=True)
        st.subheader("📊 تصور الديون")
        fig = create_debt_visualization(df)
        st.plotly_chart(fig, use_container_width=True)
        st.markdown('</div>', unsafe_allow_html=True)
        
        # بطاقة توزيع الديون
        st.markdown('<div class="dashboard-card">', unsafe_allow_html=True)
        st.subheader("📈 توزيع الديون")
        distribution_fig = create_debt_distribution_chart(df)
        st.plotly_chart(distribution_fig, use_container_width=True)
        st.markdown('</div>', unsafe_allow_html=True)

elif page == "التقارير":
    st.header("تقارير الديون")
    
    if st.session_state.debt_manager.is_empty():
        st.info("لم يتم العثور على سجلات ديون. أضف بعض السجلات أولاً.")
    else:
        # بطاقة أزرار تصدير وطباعة التقارير
        st.markdown('<div class="dashboard-card">', unsafe_allow_html=True)
        st.subheader("📊 إدارة التقارير")
        
        col1, col2, col3 = st.columns([1, 1, 2])
        with col1:
            if st.button("📄 تصدير التقرير PDF", type="primary"):
                # الحصول على بيانات الديون
                df = st.session_state.debt_manager.get_dataframe()
                
                # إنشاء ملف PDF
                try:
                    # الحصول على بيانات PDF وضمان أنها لن تكون None
                    pdf_bytes = create_all_students_report(df)
                    
                    if pdf_bytes:
                        # توفير رابط التنزيل
                        st.download_button(
                            label="تنزيل تقرير PDF",
                            data=pdf_bytes,
                            file_name=f"all_students_report_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
                            mime="application/pdf"
                        )
                        st.success("تم إنشاء تقرير PDF بنجاح!")
                    else:
                        # لن يحدث هذا لأن الدالة محسنة لإرجاع بيانات صالحة دائمًا ولكن نضيف احتياطًا
                        st.error("فشل إنشاء ملف PDF. لا توجد بيانات مرجعة.")
                except Exception as e:
                    st.error(f"فشل إنشاء تقرير PDF: {str(e)}")
                    # إضافة مزيد من المعلومات للتصحيح
                    st.error(f"نوع الخطأ: {type(e).__name__}")
        
        with col2:
            if st.button("🖨️ طباعة التقرير", help="طباعة تقرير الديون"):
                # الحصول على بيانات الديون
                df = st.session_state.debt_manager.get_dataframe()
                
                try:
                    # إنشاء ملف PDF باستخدام المكتبة البسيطة
                    pdf_bytes = create_all_students_report(df)
                    
                    # فتح التقرير في نافذة جديدة للطباعة
                    if pdf_bytes:
                        try:
                            # التعامل مع PDF كبيانات ثنائية مباشرة
                            pdf_bytes_copy = pdf_bytes
                            if hasattr(pdf_bytes, 'read'):
                                pdf_bytes_copy = pdf_bytes.read()
                            
                            # ترميز البيانات
                            b64_pdf = base64.b64encode(pdf_bytes_copy).decode()
                            pdf_display = f'<iframe src="data:application/pdf;base64,{b64_pdf}" width="100%" height="600" type="application/pdf"></iframe>'
                            
                            # عرض واجهة الطباعة
                            st.markdown("### طباعة التقرير")
                            st.markdown(pdf_display, unsafe_allow_html=True)
                        except Exception as e:
                            st.error(f"خطأ في معالجة ملف PDF: {str(e)}")
                            st.error(f"نوع البيانات: {type(pdf_bytes)}")
                    else:
                        st.error("فشل إنشاء ملف PDF. لا توجد بيانات مرجعة.")
                    
                    # إضافة زر الطباعة الفعلي باستخدام JavaScript
                    js_print = """
                    <script>
                        function printPDF() {
                            window.frames[0].focus();
                            window.frames[0].print();
                        }
                    </script>
                    <button onclick="printPDF();" class="button">🖨️ اطبع الآن</button>
                    """
                    st.markdown(js_print, unsafe_allow_html=True)
                    
                except Exception as e:
                    st.error(f"فشل إنشاء تقرير PDF للطباعة: {str(e)}")
        
        st.markdown('</div>', unsafe_allow_html=True)  # إغلاق بطاقة إدارة التقارير
        
        # بطاقة الإحصائيات الملخصة
        st.markdown('<div class="dashboard-card">', unsafe_allow_html=True)
        st.subheader("📈 إحصائيات ملخصة")
        
        # الحصول على بيانات الديون
        df = st.session_state.debt_manager.get_dataframe()
        
        # صف 1: إجماليات
        col1, col2, col3 = st.columns(3)
        col1.metric("إجمالي الديون", format_currency(df['Debt Amount'].sum()))
        col2.metric("إجمالي المسدد", format_currency(df['Paid Amount'].sum()))
        col3.metric("إجمالي المتبقي", format_currency(df['Remaining Amount'].sum()))
        
        # صف 2: المتوسطات وعدد الطلاب
        col1, col2, col3 = st.columns(3)
        col1.metric("متوسط الدين", format_currency(df['Debt Amount'].mean()))
        
        # حساب نسبة السداد
        payment_percentage = 0
        if df['Debt Amount'].sum() > 0:
            payment_percentage = (df['Paid Amount'].sum() / df['Debt Amount'].sum() * 100)
        
        col2.metric("نسبة السداد", f"{payment_percentage:.1f}%")
        col3.metric("عدد الطلاب", len(df))
        
        # عرض شريط التقدم للتحصيل
        st.markdown(f"""
        <div style="margin: 20px 0;">
            <p style="margin-bottom: 5px; text-align: center; font-weight: bold;">نسبة التحصيل الإجمالية: {payment_percentage:.1f}%</p>
            <div style="width: 100%; background-color: #e0e0e0; border-radius: 5px;">
                <div style="width: {min(payment_percentage, 100)}%; background-color: {'#4caf50' if payment_percentage >= 75 else '#ff9800' if payment_percentage >= 30 else '#f44336'}; 
                    height: 25px; border-radius: 5px; text-align: center; line-height: 25px; color: white; font-weight: bold;">
                    {payment_percentage:.1f}%
                </div>
            </div>
        </div>
        """, unsafe_allow_html=True)
        
        # إغلاق بطاقة الإحصائيات الملخصة
        st.markdown('</div>', unsafe_allow_html=True)
        
        # بطاقة أعلى المدينين
        st.markdown('<div class="dashboard-card">', unsafe_allow_html=True)
        st.subheader("🔝 أعلى 5 ديون")
        top_debtors = df.sort_values(by='Remaining Amount', ascending=False).head(5)
        
        # تنسيق بيانات العملة
        formatted_top_debtors = top_debtors.copy()
        for col in ['Debt Amount', 'Paid Amount', 'Remaining Amount']:
            formatted_top_debtors[col] = formatted_top_debtors[col].apply(lambda x: format_currency(x))
            
        # إضافة عمود نسبة المدفوع لكل طالب
        if not formatted_top_debtors.empty:
            payment_percentages = []
            for _, row in top_debtors.iterrows():
                if row['Debt Amount'] > 0:
                    payment_percentages.append(f"{(row['Paid Amount'] / row['Debt Amount'] * 100):.1f}%")
                else:
                    payment_percentages.append("0.0%")
            formatted_top_debtors['نسبة السداد'] = payment_percentages
            
        st.table(formatted_top_debtors)
        st.markdown('</div>', unsafe_allow_html=True)
        
        # بطاقة تحليل نطاق الدين بالفلس
        st.markdown('<div class="dashboard-card">', unsafe_allow_html=True)
        st.subheader("📊 تحليل نطاق الدين بالفلس")
        
        # إنشاء نطاقات الدين بالفلس
        ranges = [0, 100000, 500000, 1000000, float('inf')]
        labels = ['0-100,000 فلس', '100,000-500,000 فلس', '500,000-1,000,000 فلس', '1,000,000+ فلس']
        
        # استخدام القيمة كما هي (الفلس) للتصنيف
        df['Debt Range'] = pd.cut(df['Debt Amount'], bins=ranges, labels=labels, right=False)
        range_counts = df['Debt Range'].value_counts().sort_index()
        
        # عرض النطاقات كعدد طلاب وكنسبة مئوية
        range_df = pd.DataFrame({'عدد الطلاب': range_counts})
        range_df['النسبة المئوية'] = (range_counts / len(df) * 100).round(1).astype(str) + '%'
        
        col1, col2 = st.columns([2, 1])
        col1.bar_chart(range_counts)
        col2.table(range_df)
        
        st.markdown('</div>', unsafe_allow_html=True)
        
        # بطاقة تقرير حالة السداد
        st.markdown('<div class="dashboard-card">', unsafe_allow_html=True)
        st.subheader("💰 تقرير حالة السداد")
        
        # الحصول على بيانات حالة السداد
        fully_paid, partially_paid, unpaid = st.session_state.debt_manager.get_payment_status_report()
        
        # حساب النسب المئوية
        total_students = len(fully_paid) + len(partially_paid) + len(unpaid)
        fully_paid_pct = 0
        partially_paid_pct = 0
        unpaid_pct = 0
        
        if total_students > 0:
            fully_paid_pct = (len(fully_paid) / total_students) * 100
            partially_paid_pct = (len(partially_paid) / total_students) * 100
            unpaid_pct = (len(unpaid) / total_students) * 100
        
        # عرض ملخص لحالة السداد
        col1, col2, col3 = st.columns(3)
        col1.metric("الطلاب المسددين بالكامل", f"{len(fully_paid)} ({fully_paid_pct:.1f}%)")
        col2.metric("الطلاب المسددين جزئياً", f"{len(partially_paid)} ({partially_paid_pct:.1f}%)")
        col3.metric("الطلاب غير المسددين", f"{len(unpaid)} ({unpaid_pct:.1f}%)")
        
        # عرض رسم بياني دائري لتوزيع حالة السداد
        if total_students > 0:
            # إنشاء مخطط دائري باستخدام Plotly
            payment_status_labels = ["مسدد بالكامل", "مسدد جزئياً", "غير مسدد"]
            payment_status_values = [len(fully_paid), len(partially_paid), len(unpaid)]
            payment_status_colors = ["#4CAF50", "#FF9800", "#F44336"]
            
            # إنشاء المخطط باستخدام go.Pie بدلاً من px.pie
            fig = go.Figure(data=[go.Pie(
                labels=payment_status_labels,
                values=payment_status_values,
                hole=0.4,
                marker=dict(colors=payment_status_colors)
            )])
            fig.update_layout(
                margin=dict(l=20, r=20, t=30, b=10),
                height=300,
                legend=dict(orientation="h", yanchor="bottom", y=0, xanchor="center", x=0.5),
            )
            st.plotly_chart(fig, use_container_width=True)
        
        # إضافة أزرار تصدير وطباعة تقرير حالة السداد
        col1, col2 = st.columns(2)
        
        with col1:
            if st.button("📄 تصدير تقرير حالة السداد PDF", type="primary"):
                try:
                    # إنشاء ملف PDF باستخدام المكتبة البسيطة
                    pdf_bytes = create_payment_status_report(fully_paid, partially_paid, unpaid)
                    
                    # توفير رابط التنزيل
                    st.download_button(
                        label="تنزيل تقرير حالة السداد PDF",
                        data=pdf_bytes,
                        file_name=f"payment_status_report_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
                        mime="application/pdf"
                    )
                    st.success("تم إنشاء تقرير حالة السداد PDF بنجاح!")
                except Exception as e:
                    st.error(f"فشل إنشاء تقرير PDF: {str(e)}")
        
        with col2:
            if st.button("🖨️ طباعة تقرير حالة السداد", help="طباعة تقرير حالة السداد"):
                try:
                    # إنشاء ملف PDF باستخدام المكتبة البسيطة
                    pdf_bytes = create_payment_status_report(fully_paid, partially_paid, unpaid)
                    
                    # فتح التقرير في نافذة جديدة للطباعة
                    if pdf_bytes:
                        try:
                            # التعامل مع PDF كبيانات ثنائية مباشرة
                            pdf_bytes_copy = pdf_bytes
                            if hasattr(pdf_bytes, 'read'):
                                pdf_bytes_copy = pdf_bytes.read()
                            
                            # ترميز البيانات
                            b64_pdf = base64.b64encode(pdf_bytes_copy).decode()
                            pdf_display = f'<iframe src="data:application/pdf;base64,{b64_pdf}" width="100%" height="600" type="application/pdf"></iframe>'
                            
                            # عرض واجهة الطباعة
                            st.markdown("### طباعة تقرير حالة السداد")
                            st.markdown(pdf_display, unsafe_allow_html=True)
                        except Exception as e:
                            st.error(f"خطأ في معالجة ملف PDF: {str(e)}")
                            st.error(f"نوع البيانات: {type(pdf_bytes)}")
                    else:
                        st.error("فشل إنشاء ملف PDF. لا توجد بيانات مرجعة.")
                    
                    # إضافة زر الطباعة الفعلي باستخدام JavaScript
                    js_print = """
                    <script>
                        function printStatusPDF() {
                            window.frames[0].focus();
                            window.frames[0].print();
                        }
                    </script>
                    <button onclick="printStatusPDF();" class="button">🖨️ اطبع تقرير حالة السداد</button>
                    """
                    st.markdown(js_print, unsafe_allow_html=True)
                    
                except Exception as e:
                    st.error(f"فشل إنشاء تقرير حالة السداد PDF للطباعة: {str(e)}")
        
        # عرض بيانات تفصيلية لكل فئة من الطلاب
        if not fully_paid.empty or not partially_paid.empty or not unpaid.empty:
            st.markdown("</div>", unsafe_allow_html=True)  # إغلاق بطاقة تقرير حالة السداد
            
            # بطاقة تفاصيل حالة السداد للطلاب
            st.markdown('<div class="dashboard-card">', unsafe_allow_html=True)
            st.subheader("👥 تفاصيل حالة السداد للطلاب")
            
            # إنشاء علامات تبويب للفئات المختلفة
            tab_names = [
                f"الطلاب المسددين بالكامل ({len(fully_paid)})",
                f"الطلاب المسددين جزئياً ({len(partially_paid)})", 
                f"الطلاب غير المسددين ({len(unpaid)})"
            ]
            tabs = st.tabs(tab_names)
            
            # الطلاب المسددين بالكامل
            with tabs[0]:
                if not fully_paid.empty:
                    # تنسيق بيانات العملة
                    display_df = fully_paid.copy()
                    for col in ['Debt Amount', 'Paid Amount', 'Remaining Amount']:
                        display_df[col] = display_df[col].apply(lambda x: format_currency(x))
                    
                    st.markdown("""
                    <div style="padding: 10px; background-color: #e7f3e8; border-radius: 5px; margin-bottom: 15px;">
                        <span style="color: #2e7d32; font-weight: bold;">✅ الطلاب الذين سددوا مديونياتهم بالكامل</span>
                    </div>
                    """, unsafe_allow_html=True)
                    
                    st.table(display_df)
                else:
                    st.info("لا يوجد طلاب مسددين بالكامل.")
            
            # الطلاب المسددين جزئياً
            with tabs[1]:
                if not partially_paid.empty:
                    # تنسيق بيانات العملة والنسب المئوية
                    display_df = partially_paid.copy()
                    # إضافة عمود لنسبة السداد للعرض
                    display_df['Payment Percentage'] = display_df['Payment Percentage'].apply(lambda x: f"{x}%")
                    for col in ['Debt Amount', 'Paid Amount', 'Remaining Amount']:
                        display_df[col] = display_df[col].apply(lambda x: format_currency(x))
                    
                    st.markdown("""
                    <div style="padding: 10px; background-color: #fff3e0; border-radius: 5px; margin-bottom: 15px;">
                        <span style="color: #e65100; font-weight: bold;">⚠️ الطلاب الذين سددوا جزء من مديونياتهم</span>
                    </div>
                    """, unsafe_allow_html=True)
                    
                    st.table(display_df)
                else:
                    st.info("لا يوجد طلاب مسددين جزئياً.")
            
            # الطلاب غير المسددين
            with tabs[2]:
                if not unpaid.empty:
                    # تنسيق بيانات العملة
                    display_df = unpaid.copy()
                    for col in ['Debt Amount', 'Paid Amount', 'Remaining Amount']:
                        display_df[col] = display_df[col].apply(lambda x: format_currency(x))
                    
                    st.markdown("""
                    <div style="padding: 10px; background-color: #ffebee; border-radius: 5px; margin-bottom: 15px;">
                        <span style="color: #c62828; font-weight: bold;">❌ الطلاب الذين لم يسددوا أي مبلغ</span>
                    </div>
                    """, unsafe_allow_html=True)
                    
                    st.table(display_df)
                else:
                    st.info("لا يوجد طلاب غير مسددين.")
                    
            st.markdown("</div>", unsafe_allow_html=True)  # إغلاق بطاقة تفاصيل حالة السداد
        else:
            st.markdown("</div>", unsafe_allow_html=True)  # إغلاق بطاقة تقرير حالة السداد

elif page == "استيراد/تصدير":
    st.header("استيراد/تصدير البيانات")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown('<div class="dashboard-card">', unsafe_allow_html=True)
        st.subheader("📥 استيراد من Excel")
        
        # زر لتحميل قالب فارغ
        if st.button("تحميل قالب فارغ"):
            # إنشاء قالب فارغ
            excel_template = generate_excel_template()
            # إنشاء رابط تنزيل
            st.download_button(
                label="تنزيل قالب Excel",
                data=excel_template,
                file_name="student_debts_template.xlsx",
                mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            )
        
        st.write("---")
        
        # إضافة خيارات الاستيراد
        import_format = st.radio(
            "اختر طريقة الاستيراد:",
            ["تنسيق عادي (أعمدة بأسماء واضحة)", "تنسيق محدد (أسماء من العمود C، مديونيات من العمود D)"]
        )
        
        # تعريف المتغيرات الافتراضية
        names_column = "C"
        debts_column = "D"
        start_row = 2
        end_row = None
        replace_existing = False
        update_existing = True
        
        # خيارات التحديث والاستبدال المشتركة لجميع طرق الاستيراد
        st.markdown("### خيارات الاستيراد المتقدمة")
        col_c, col_d = st.columns(2)
        with col_c:
            replace_existing = st.checkbox("مسح جميع البيانات الحالية قبل الاستيراد", value=False)
            if replace_existing:
                st.warning("⚠️ هذا سيؤدي إلى حذف جميع البيانات الحالية!")
        with col_d:
            update_existing = st.checkbox("تحديث السجلات الموجودة بالفعل", value=True)
            if not update_existing:
                st.info("سيتم تجاهل الطلاب الموجودين مسبقاً")
        
        # خيارات متقدمة للتنسيق المحدد
        if import_format == "تنسيق محدد (أسماء من العمود C، مديونيات من العمود D)":
            st.info("سيتم استيراد البيانات من عمود C للأسماء وعمود D للمديونيات بدءًا من الصف الثاني (C2, D2)")
            
            col_a, col_b = st.columns(2)
            with col_a:
                names_column = st.text_input("عمود الأسماء", value="C")
                start_row = st.number_input("بدء من الصف", value=2, min_value=1)
            with col_b:
                debts_column = st.text_input("عمود المديونيات", value="D")
                use_end_row = st.checkbox("تحديد صف نهائي", value=False)
            
            if use_end_row:
                end_row = st.number_input("الصف النهائي (ينتهي الاستيراد عنده)", value=50, min_value=start_row)
            
            # معلومات حول التنسيق
            st.write("ملاحظة: المبالغ ستكون بالفلس")
        
        uploaded_file = st.file_uploader("اختر ملف Excel", type=["xlsx", "xls"])
        
        if uploaded_file is not None:
            try:
                # استيراد البيانات من Excel حسب التنسيق المحدد
                if import_format == "تنسيق محدد (أسماء من العمود C، مديونيات من العمود D)":
                    # استخدام التنسيق المحدد
                    if st.session_state.debt_manager.import_from_excel(
                        uploaded_file, 
                        use_specific_format=True,
                        names_col=names_column,
                        debts_col=debts_column,
                        start_row=int(start_row),
                        end_row=int(end_row) if end_row is not None else None,
                        replace_existing=replace_existing,
                        update_existing=update_existing
                    ):
                        st.success(f"تم استيراد البيانات بنجاح من العمود {names_column} للأسماء والعمود {debts_column} للمديونيات!")
                    else:
                        st.error("فشل استيراد البيانات. يرجى التحقق من تنسيق الملف والأعمدة المحددة.")
                else:
                    # استخدام التنسيق العادي
                    if st.session_state.debt_manager.import_from_excel(
                        uploaded_file,
                        replace_existing=replace_existing,
                        update_existing=update_existing
                    ):
                        st.success("تم استيراد البيانات بنجاح!")
                    else:
                        st.error("فشل استيراد البيانات. يرجى التحقق من تنسيق الملف.")
            except Exception as e:
                st.error(f"خطأ في استيراد الملف: {str(e)}")
        
        # إغلاق بطاقة الاستيراد
        st.markdown('</div>', unsafe_allow_html=True)
    
    with col2:
        # بطاقة تصدير وإدارة البيانات
        st.markdown('<div class="dashboard-card">', unsafe_allow_html=True)
        st.subheader("📤 تصدير وإدارة البيانات")
        
        if st.session_state.debt_manager.is_empty():
            st.info("لا توجد بيانات للتصدير. أضف بعض السجلات أولاً.")
        else:
            export_file_name = st.text_input("اسم ملف التصدير (بدون امتداد)", "student_debts_export")
            
            if st.button("تصدير البيانات"):
                try:
                    # تصدير البيانات إلى Excel
                    file_path = f"{export_file_name}.xlsx"
                    if st.session_state.debt_manager.export_to_excel(file_path):
                        # إنشاء رابط تنزيل
                        with open(file_path, "rb") as file:
                            st.download_button(
                                label="تنزيل ملف Excel",
                                data=file,
                                file_name=file_path,
                                mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                            )
                        # تنظيف الملف بعد التحضير للتنزيل
                        if os.path.exists(file_path):
                            os.remove(file_path)
                    else:
                        st.error("فشل تصدير البيانات.")
                except Exception as e:
                    st.error(f"خطأ في تصدير البيانات: {str(e)}")
            
            # إغلاق بطاقة التصدير العادية
            st.markdown('</div>', unsafe_allow_html=True)
            
            # بطاقة إفراغ البيانات بنمط خطر
            st.markdown('<div class="dashboard-card" style="border: 2px solid #f44336; background-color: #ffebee;">', unsafe_allow_html=True)
            st.subheader("⚠️ إفراغ البيانات")
            st.warning("تحذير: هذه العملية ستحذف جميع البيانات ولا يمكن التراجع عنها!")
            
            # عملية تأكيد إفراغ البيانات على مرحلتين
            if 'clear_data_confirm' not in st.session_state:
                st.session_state.clear_data_confirm = False
                
            if not st.session_state.clear_data_confirm:
                if st.button("إفراغ جميع البيانات", type="primary"):
                    st.session_state.clear_data_confirm = True
                    st.rerun()
            else:
                st.error("هل أنت متأكد من إفراغ جميع البيانات؟ لا يمكن التراجع عن هذا الإجراء!")
                col1, col2 = st.columns(2)
                
                if col1.button("نعم، إفراغ جميع البيانات", type="primary"):
                    if st.session_state.debt_manager.clear_all_data():
                        st.success("تم إفراغ جميع البيانات بنجاح")
                        st.session_state.clear_data_confirm = False
                        st.rerun()
                    else:
                        st.error("فشل إفراغ البيانات")
                        
                if col2.button("إلغاء"):
                    st.session_state.clear_data_confirm = False
                    st.rerun()
            
            # إغلاق بطاقة إفراغ البيانات
            st.markdown('</div>', unsafe_allow_html=True)

# التذييل
st.markdown("---")
st.markdown("""
<div style="text-align: center; color: #666; padding: 20px 0;">
    <p>© 2023-2025 نظام إدارة ديون الطلاب</p>
    <p style="font-size: 0.8em;">تم التطوير بواسطة تقنيات حديثة وبمعايير عالية الجودة</p>
</div>
""", unsafe_allow_html=True)
